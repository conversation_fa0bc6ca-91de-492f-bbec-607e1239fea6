using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
//using {ParentTableNamespace}.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class WorkflowMapConfig : EntityMappingConfiguration<Workflow>
    {
    public override void Map(EntityTypeBuilder<Workflow>
        builderTable)
        {
           //builderTable.HasOne<{ParentTableName}>(o => o.{TableTrueName}).WithOne().HasForeignKey<{ParentTableName}>(e => e.{Key});
           //builderTable.Navigation(o => o.{TableTrueName}).IsRequired();
        }
     }
}

