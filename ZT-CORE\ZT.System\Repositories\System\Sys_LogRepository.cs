﻿using ZT.System.IRepositories;
using ZT.Core.BaseProvider;
using ZT.Core.Extensions.AutofacManager;
using ZT.Core.EFDbContext;
using ZT.Entity.DomainModels;

namespace ZT.System.Repositories
{
    public partial class Sys_LogRepository : RepositoryBase<Sys_Log>, ISys_LogRepository
    {
        public Sys_LogRepository(VOLContext dbContext)
        : base(dbContext)
        {

        }
        public static ISys_LogRepository GetService
        {
            get { return AutofacContainerModule.GetService<ISys_LogRepository>(); }
        }
    }
}

