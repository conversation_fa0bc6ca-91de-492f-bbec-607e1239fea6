/*
*所有关于Sys_Job类的业务代码接口应在此处编写
*/

using ZT.System.DomainModels;
using System.Collections.Generic;
using ZT.Core.Utilities;

namespace ZT.System.IServices
{
    public partial interface ISys_JobService
    {
        /// <summary>
        /// 获取任务
        /// </summary>
        /// <param name="state">-1 全部 1 正常 0 暂停</param>
        /// <returns></returns>
        List<Sys_Job> Gets(int state = 1);
        /// <summary>
        /// 获取结束时间大于当前时间的任务
        /// </summary>
        /// <param name="state">-1 全部 1 正常 0 暂停</param>
        /// <returns></returns>
        List<Sys_Job> GetsGreaterThanNow(int state = 1);
        /// <summary>
        /// 保存任务执行结果
        /// </summary>
        /// <param name = "job"></param>
        /// <returns ></returns>
        void SaveResult(Sys_Job job);
        /// <summary>
        /// 恢复job
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        WebResponseContent Resume(int id);
        /// <summary>
        /// 暂停
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        WebResponseContent Pause(int id);
    }
}
