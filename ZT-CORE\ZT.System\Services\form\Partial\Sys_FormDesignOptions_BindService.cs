/*
 *所有关于Sys_FormDesignOptions_Bind类的业务代码应在此处编写
*可使用repository.调用常用方法，获取EF/Dapper等信息
*如果需要事务请使用repository.DbContextBeginTransaction
*也可使用DBServerProvider.手动获取数据库相关信息
*用户信息、权限、角色等使用UserContext.Current操作
*Sys_FormDesignOptions_BindService对增、删、改查、导入、导出、审核业务代码扩展参照ServiceFunFilter
*/
using ZT.Core.BaseProvider;
using ZT.Core.Extensions.AutofacManager;
using ZT.System.DomainModels;
using System.Linq;
using ZT.Core.Utilities;
using System.Linq.Expressions;
using ZT.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using ZT.System.IRepositories;
using ZT.Entity.DomainModels;
using System.Collections.Generic;
using ZT.Core.DBManager;
using ZT.Core.Enums;

namespace ZT.System.Services
{
    public partial class Sys_FormDesignOptions_BindService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ISys_FormDesignOptions_BindRepository _repository;//访问数据库

        [ActivatorUtilitiesConstructor]
        public Sys_FormDesignOptions_BindService(
            ISys_FormDesignOptions_BindRepository dbRepository,
            IHttpContextAccessor httpContextAccessor
            )
        : base(dbRepository)
        {
            _httpContextAccessor = httpContextAccessor;
            _repository = dbRepository;
            //多租户会用到这init代码，其他情况可以不用
            //base.Init(dbRepository);
        }

        public WebResponseContent Save(SaveModel saveDataModel)
        {
            if (!saveDataModel.MainData.TryGetValue("BINDTABLE", out object bindTable) || string.IsNullOrEmpty(bindTable.ToString()))
            {
                return new WebResponseContent().Error("参数不全，BINDTABLE未找到");
            }
            if (!saveDataModel.MainData.TryGetValue("BINDTABLEID", out object bindTableId) || string.IsNullOrEmpty(bindTableId.ToString()))
            {
                return new WebResponseContent().Error("参数不全，BINDTABLEID未找到");
            }
            var listAdd = saveDataModel.DetailData.Where(c => c["__optype"]?.ToString() == "A").ToList();
            var listUpd = saveDataModel.DetailData.Where(c => c["__optype"]?.ToString() == "U").ToList();
            var listDel = saveDataModel.DelKeys;
            if (listAdd != null && listAdd.Count > 0)
            {
                List<Sys_FormDesignOptions_Bind> list = listAdd.DicToList<Sys_FormDesignOptions_Bind>();
                list.ForEach(c => { c.BINDTABLE = bindTable.ToString(); c.BINDTABLEID = bindTableId.ToString(); c.BINDNAME = saveDataModel.MainData["BINDNAME"].ToString(); c.BINDREVISION = saveDataModel.MainData["BINDREVISION"]?.ToString(); });
                repository.AddRange(list, true);
            }
            if (listUpd != null && listUpd.Count > 0)
            {
                List<Sys_FormDesignOptions_Bind> list = listUpd.DicToList<Sys_FormDesignOptions_Bind>();
                list.ForEach(c => { c.BINDTABLE = bindTable.ToString(); c.BINDTABLEID = bindTableId.ToString(); c.BINDNAME = saveDataModel.MainData["BINDNAME"].ToString(); c.BINDREVISION = saveDataModel.MainData["BINDREVISION"]?.ToString(); });
                repository.UpdateRange(list, true);
            }
            if (listDel!=null&&listDel.Count>0)
            {
                repository.DeleteWithKeys(listDel.ToArray());
            }
            return new WebResponseContent().OK();
        }

        public WebResponseContent GetBindFormData(PageDataOptions loadData) {
            string Sql = @"select DISTINCT a.BINDTABLE from Sys_FormDesignOptions_Bind a ";
            //从标准功能获取数据
            var data = DBServerProvider.SqlDapper.QueryDynamicList(Sql, null);
            return new WebResponseContent() { data = data }.OK(ResponseType.QuerySuccess);
        }

        public WebResponseContent GetBindFormObjectData(string bindTable)
        {
            string Sql = @"select DISTINCT a.BINDTABLEID,a.BINDNAME,a.BINDREVISION from Sys_FormDesignOptions_Bind a where a.BINDTABLE=:bindTable";
            //从标准功能获取数据
            var data = DBServerProvider.SqlDapper.QueryDynamicList(Sql, new { bindTable = bindTable });
            return new WebResponseContent() { data = data }.OK(ResponseType.QuerySuccess);
        }

        public WebResponseContent GetBindFormIdData(string bindTableId)
        {
            string Sql = @"select * from Sys_FormDesignOptions s where s.FORMID in (select a.FORMID from Sys_FormDesignOptions_Bind a where a.BINDTABLEID=:bindTableId) and s.formstatus ='1'";
            //从标准功能获取数据
            var data = DBServerProvider.SqlDapper.QueryDynamicList(Sql, new { bindTableId = bindTableId });
            return new WebResponseContent() { data = data }.OK(ResponseType.QuerySuccess);
        }
    }
}
