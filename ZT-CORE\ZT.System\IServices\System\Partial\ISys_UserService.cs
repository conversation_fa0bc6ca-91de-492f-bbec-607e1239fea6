﻿using ZT.Core.BaseProvider;
using ZT.Core.Utilities;
using ZT.Entity.DomainModels;
using System.Threading.Tasks;

namespace ZT.System.IServices
{
    public partial interface ISys_UserService
    {

        Task<WebResponseContent> Login(LoginInfo loginInfo, bool isDomainLogin = false, bool verificationCode = true);
        Task<WebResponseContent> SingleLogin(LoginInfo loginInfo, bool verificationCode = true);
        object CardLogin(string cardNum, bool verificationCode = true);
        Task<WebResponseContent> ReplaceToken();
        Task<WebResponseContent> ModifyPwd(string oldPwd, string newPwd, string key);
        Task<WebResponseContent> GetCurrentUserInfo();
        /// <summary>
        /// 选择人员信息
        /// </summary>
        /// <returns></returns>
        public WebResponseContent EmployeeTree();
        /// <summary>
        /// 用户角色管理角色树可搜索
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        public WebResponseContent GetUserInfo(string userName);

        /// <summary>
        /// 为工作流提供查询用户列表
        /// </summary>
        /// <param name="realName"> 人员姓名 </param>
        /// <returns></returns>
        public object GetUserList4Workflow(string realName);
        /// <summary>
        /// 为工作流提供查询用户
        /// </summary>
        /// <param name="userName"> 人员账号 </param>
        public object GetUserByName4Workflow(string userName);

        /// <summary>
        /// 为工作流提供查询用户
        /// </summary>
        /// <param name="id"> 人员id </param>
        public object GetUserById4Workflow(string id);

        public WebResponseContent SaveUserRole(SaveModel saveModel);
    }
}

