/*
*所有关于Sys_Setting类的业务代码接口应在此处编写
*/
using ZT.Core.BaseProvider;
using ZT.System.DomainModels;
using ZT.Core.Utilities;
using System.Linq.Expressions;
using ZT.System.DomainModels.ApiEntity.input;

namespace ZT.System.IServices
{
    public partial interface ISys_SettingService
    {
        public WebResponseContent Save(ApiSys_SettingInput apiSetting);
        public WebResponseContent Read(ApiSys_SettingInput apiSetting);
        public WebResponseContent Delete(ApiSys_SettingInput apiSetting);
    }
 }
