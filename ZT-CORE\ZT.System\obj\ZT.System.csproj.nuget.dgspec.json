{"format": 1, "restore": {"D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.System\\ZT.System.csproj": {}}, "projects": {"D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Core\\ZT.Core.csproj": {"version": "2.1.0", "restore": {"projectUniqueName": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Core\\ZT.Core.csproj", "projectName": "ZT.Core", "projectPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Core\\ZT.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\DevExpress 22.2\\Components\\Offline Packages", "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\Program Files\\DevExpress 22.2\\Components\\System\\Components\\Packages": {}, "http://10.145.30.97:5000/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Entity\\ZT.Entity.csproj": {"projectPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Entity\\ZT.Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[11.0.1, )"}, "Autofac": {"target": "Package", "version": "[6.0.0, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "CSRedisCore": {"target": "Package", "version": "[3.6.6, )"}, "Dapper": {"target": "Package", "version": "[2.1.35, )"}, "EFCore.BulkExtensions": {"target": "Package", "version": "[2.2.2, )"}, "EPPlus.Core": {"target": "Package", "version": "[1.5.4, )"}, "Microsoft.AspNetCore.Mvc.Formatters.Json": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.Proxies": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.Extensions.Caching.Redis.Core": {"target": "Package", "version": "[1.0.3, )"}, "Microsoft.Extensions.DependencyModel": {"target": "Package", "version": "[7.0.0, )"}, "NLog": {"target": "Package", "version": "[5.3.2, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[5.3.11, )"}, "Npgsql": {"target": "Package", "version": "[8.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[3.1.0, )"}, "Oracle.EntityFrameworkCore": {"target": "Package", "version": "[5.21.61, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[5.0.1, )"}, "ServiceStack": {"target": "Package", "version": "[8.3.0, )"}, "SqlSugarCore": {"target": "Package", "version": "[5.1.4.169, )"}, "WorkflowCore": {"target": "Package", "version": "[3.11.0, )"}, "WorkflowCore.Users": {"target": "Package", "version": "[3.11.0, )"}, "ZKWeb.System.Drawing": {"target": "Package", "version": "[4.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Entity\\ZT.Entity.csproj": {"version": "2.1.0", "restore": {"projectUniqueName": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Entity\\ZT.Entity.csproj", "projectName": "ZT.Entity", "projectPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Entity\\ZT.Entity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Entity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\DevExpress 22.2\\Components\\Offline Packages", "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\Program Files\\DevExpress 22.2\\Components\\System\\Components\\Packages": {}, "http://10.145.30.97:5000/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[11.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.EntityFrameworkCore.Proxies": {"target": "Package", "version": "[7.0.20, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[3.1.0, )"}, "SqlSugarCore": {"target": "Package", "version": "[5.1.4.169, )"}, "WorkflowCore": {"target": "Package", "version": "[3.11.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}, "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.System\\ZT.System.csproj": {"version": "2.1.0", "restore": {"projectUniqueName": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.System\\ZT.System.csproj", "projectName": "ZT.System", "projectPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.System\\ZT.System.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.System\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\DevExpress 22.2\\Components\\Offline Packages", "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 22.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\Program Files\\DevExpress 22.2\\Components\\System\\Components\\Packages": {}, "http://10.145.30.97:5000/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Core\\ZT.Core.csproj": {"projectPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Core\\ZT.Core.csproj"}, "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Entity\\ZT.Entity.csproj": {"projectPath": "D:\\DevCode\\ZT_InlineWarehouse\\ZT-CORE\\ZT.Entity\\ZT.Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[11.0.1, )"}, "CSRedisCore": {"target": "Package", "version": "[3.6.6, )"}, "EFCore.BulkExtensions": {"target": "Package", "version": "[2.2.2, )"}, "EPPlus.Core": {"target": "Package", "version": "[1.5.4, )"}, "Microsoft.AspNetCore.Mvc.Formatters.Json": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.EntityFrameworkCore.Proxies": {"target": "Package", "version": "[7.0.20, )"}, "Microsoft.Extensions.Caching.Redis.Core": {"target": "Package", "version": "[1.0.3, )"}, "Microsoft.Extensions.DependencyModel": {"target": "Package", "version": "[7.0.0, )"}, "Quartz.AspNetCore": {"target": "Package", "version": "[3.13.0, )"}, "Quartz.Extensions.Hosting": {"target": "Package", "version": "[3.13.0, )"}, "WorkflowCore": {"target": "Package", "version": "[3.11.0, )"}, "WorkflowCore.Users": {"target": "Package", "version": "[3.11.0, )"}, "ZKWeb.System.Drawing": {"target": "Package", "version": "[4.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101\\RuntimeIdentifierGraph.json"}}}}}