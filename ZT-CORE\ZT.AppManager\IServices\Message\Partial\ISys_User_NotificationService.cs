/*
*所有关于Sys_User_Notification类的业务代码接口应在此处编写
*/
using ZT.Core.BaseProvider;
using ZT.AppManager.DomainModels;
using ZT.Core.Utilities;
using System.Linq.Expressions;
using System.Collections.Generic;

namespace ZT.AppManager.IServices
{
    public partial interface ISys_User_NotificationService
    {
        /// <summary>
        /// 获取当前用户的通知
        /// </summary>
        /// <returns></returns>
       List<Sys_User_Notification> GetNotificationsByUser(int userid);

        /// <summary>
        /// 根据notificationId获取所有用户的通知
        /// </summary>
        /// <returns></returns>
        List<Sys_User_Notification> GetNotificationsByNotification(int notificationid);
        WebResponseContent MarkReadAll();
        WebResponseContent MarkRead(List<int> userNotifiIdList);
    }
}
