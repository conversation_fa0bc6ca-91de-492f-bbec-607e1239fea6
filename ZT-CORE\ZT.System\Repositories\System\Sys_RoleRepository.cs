﻿/*
 *Author：jxx
 *Contact：<EMAIL>
 *Date：2018-07-01
 * 此代码由框架生成，请勿随意更改
 */
using ZT.System.IRepositories;
using ZT.Core.BaseProvider;
using ZT.Core.EFDbContext;
using ZT.Core.Extensions.AutofacManager;
using ZT.Entity.DomainModels;

namespace ZT.System.Repositories
{
    public partial class Sys_RoleRepository : RepositoryBase<Sys_Role>, ISys_RoleRepository
    {
        public Sys_RoleRepository(VOLContext dbContext)
        : base(dbContext)
        {

        }
        public static ISys_RoleRepository Instance
        {
            get { return AutofacContainerModule.GetService<ISys_RoleRepository>(); }
        }
    }
}

