using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class Sys_FormDesignOptions_BindMapConfig : EntityMappingConfiguration<Sys_FormDesignOptions_Bind>
    {
    public override void Map(EntityTypeBuilder<Sys_FormDesignOptions_Bind>
        builderTable)
        {
        //b.Property(x => x.StorageName).HasMaxLength(45);
        }
        }
        }

