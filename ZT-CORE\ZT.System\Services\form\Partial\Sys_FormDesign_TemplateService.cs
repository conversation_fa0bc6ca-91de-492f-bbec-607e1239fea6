/*
 *所有关于Sys_FormDesign_Template类的业务代码应在此处编写
*可使用repository.调用常用方法，获取EF/Dapper等信息
*如果需要事务请使用repository.DbContextBeginTransaction
*也可使用DBServerProvider.手动获取数据库相关信息
*用户信息、权限、角色等使用UserContext.Current操作
*Sys_FormDesign_TemplateService对增、删、改查、导入、导出、审核业务代码扩展参照ServiceFunFilter
*/
using ZT.Core.BaseProvider;
using ZT.Core.Extensions.AutofacManager;
using ZT.System.DomainModels;
using System.Linq;
using ZT.Core.Utilities;
using System.Linq.Expressions;
using ZT.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using ZT.System.IRepositories;
using ZT.Core.EFDbContext;
using ZT.Entity.DomainModels;
using ZT.Core.DBManager;
using System;
using System.Text;
using ZT.Core.Enums;

namespace ZT.System.Services
{
    public partial class Sys_FormDesign_TemplateService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ISys_FormDesign_TemplateRepository _repository;//访问数据库

        [ActivatorUtilitiesConstructor]
        public Sys_FormDesign_TemplateService(
            ISys_FormDesign_TemplateRepository dbRepository,
            IHttpContextAccessor httpContextAccessor
            )
        : base(dbRepository)
        {
            _httpContextAccessor = httpContextAccessor;
            _repository = dbRepository;
            //多租户会用到这init代码，其他情况可以不用
            //base.Init(dbRepository);
        }


        public override WebResponseContent Add(SaveModel saveModel)
        {
            WebResponseContent responseData = new WebResponseContent();

            Sys_FormDesign_Template mainEntity = saveModel.MainData.DicToEntity<Sys_FormDesign_Template>();

            int num = Convert.ToInt32(Math.Ceiling((float)mainEntity.JSONSTR.Length / 3000));
            StringBuilder sb = new StringBuilder("''");
            for (int i = 0; i < num; i++)
            {
                sb.Append($"||to_clob('{mainEntity.JSONSTR.Substring(i * 3000, i == num - 1 ? mainEntity.JSONSTR.Length - i * 3000 : 3000)}')");
            }

            string sqlstr = $"INSERT INTO Sys_FormDesign_Template(TITLE,IMGURL,jsonstr) VALUES(:title,:imgurl,{sb.ToString()})";
            try
            {
                //repository.DbContext.Database.ExecuteSqlRaw(sqlstr, new { title = mainEntity.TITLE, imgurl = mainEntity.IMGURL });
                DBServerProvider.SqlDapper.ExcuteNonQuery(sqlstr, new { title = mainEntity.TITLE, imgurl = mainEntity.IMGURL });
                return responseData.OK(ResponseType.SaveSuccess);
            }
            catch (Exception ex)
            {
                return responseData.Error(ex.Message);
            }
        }

        public override WebResponseContent Update(SaveModel saveModel)
        {
            WebResponseContent responseData = new WebResponseContent();

            Sys_FormDesign_Template mainEntity = saveModel.MainData.DicToEntity<Sys_FormDesign_Template>();

            int num = Convert.ToInt32(Math.Ceiling((float)mainEntity.JSONSTR.Length / 3000));
            StringBuilder sb = new StringBuilder("''");
            for (int i = 0; i < num; i++)
            {
                sb.Append($"||to_clob('{mainEntity.JSONSTR.Substring(i * 3000, i == num - 1 ? mainEntity.JSONSTR.Length - i * 3000 : 3000)}')");
            }

            string sqlstr = $"UPDATE Sys_FormDesign_Template set TITLE=:title, IMGURL=:imgurl,jsonstr={sb.ToString()} WHERE ID=:id";
            try
            {
                DBServerProvider.SqlDapper.ExcuteNonQuery(sqlstr, new { title = mainEntity.TITLE, imgurl = mainEntity.IMGURL, id = mainEntity.ID });
                return responseData.OK(ResponseType.SaveSuccess);
            }
            catch (Exception ex)
            {
                return responseData.Error(ex.Message);
            }
        }
    }
}
