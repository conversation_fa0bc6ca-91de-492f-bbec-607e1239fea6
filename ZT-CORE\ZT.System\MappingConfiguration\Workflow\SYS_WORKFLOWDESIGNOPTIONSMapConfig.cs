using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
//using {ParentTableNamespace}.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class SYS_WORKFLOWDESIGNOPTIONSMapConfig : EntityMappingConfiguration<SYS_WORKFLOWDESIGNOPTIONS>
    {
    public override void Map(EntityTypeBuilder<SYS_WORKFLOWDESIGNOPTIONS>
        builderTable)
        {
           //builderTable.HasOne<{ParentTableName}>(o => o.{TableTrueName}).WithOne().HasForeignKey<{ParentTableName}>(e => e.{Key});
           //builderTable.Navigation(o => o.{TableTrueName}).IsRequired();
        }
     }
}

