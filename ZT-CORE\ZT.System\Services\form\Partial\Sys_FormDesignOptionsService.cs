/*
 *所有关于Sys_FormDesignOptions类的业务代码应在此处编写
*可使用repository.调用常用方法，获取EF/Dapper等信息
*如果需要事务请使用repository.DbContextBeginTransaction
*也可使用DBServerProvider.手动获取数据库相关信息
*用户信息、权限、角色等使用UserContext.Current操作
*Sys_FormDesignOptionsService对增、删、改查、导入、导出、审核业务代码扩展参照ServiceFunFilter
*/
using ZT.Core.BaseProvider;
using ZT.Core.Extensions.AutofacManager;
using ZT.Entity.DomainModels;
using System.Linq;
using ZT.Core.Utilities;
using System.Linq.Expressions;
using ZT.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using ZT.System.IRepositories;
using System;
using System.Text;
using ZT.Core.DBManager;
using ZT.Core.ManageUser;
using ZT.System.DomainModels;
using ZT.Core.Enums;
using ZT.Core.Infrastructure;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.Dynamic;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Collections.Generic;
using ZT.Entity;
using EFCore.BulkExtensions;
using System.Collections;
using Microsoft.Data.SqlClient.DataClassification;
using Microsoft.AspNetCore.Mvc;
using ZT.Core.BaseInterface.CamstarAPI;
using ZT.Entity.DomainModels.Mes;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ZT.System.Services
{
    public partial class Sys_FormDesignOptionsService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ISys_FormDesignOptionsRepository _repository;//访问数据库

        [ActivatorUtilitiesConstructor]
        public Sys_FormDesignOptionsService(
            ISys_FormDesignOptionsRepository dbRepository,
            IHttpContextAccessor httpContextAccessor
            )
        : base(dbRepository)
        {
            _httpContextAccessor = httpContextAccessor;
            _repository = dbRepository;
            //多租户会用到这init代码，其他情况可以不用
            //base.Init(dbRepository);
        }
        public override WebResponseContent Copy(SaveModel saveDataModel)
        {
            if (saveDataModel.MainData.TryGetValue("OBJECTTOCHANGE", out object obj) && obj != null)
            {
                //传入OBJECTTOCHANGE是源对象
                //传入的NAME，REVISION是新对象的名称和版本
                //1.先判断NAME，REVISION在数据库中是否已经存在，不允许存在
                //2.查询出源对象，插入新对象
                if ((saveDataModel.MainData.TryGetValue("NAME", out object nameObj) && !string.IsNullOrEmpty(nameObj.ToString()))
                    && (saveDataModel.MainData.TryGetValue("REVISION", out object revisionObj) && !string.IsNullOrEmpty(revisionObj.ToString())))
                {
                    string name = nameObj.ToString();
                    string revision = revisionObj.ToString();
                    //1.先判断NAME，REVISION在数据库中是否已经存在，不允许存在
                    if (repository.Exists(x => x.TITLE == name && x.FORMREVISION == revision))
                    {
                        //NAME:name,REVISION:revision已存在
                        return new WebResponseContent().Error(LanguageManager.Translate("common.alreadyExists", LanguageManager.Translate("CamstarBaseObject.targetInstanceName") + ":" + name + "," + LanguageManager.Translate("CamstarBaseObject.targetInstanceRev") + ":" + revision));
                    }
                    //2.查询出源对象，插入新对象
                    var sourceEntity = Newtonsoft.Json.JsonConvert.DeserializeObject<CamstarBaseObject>(obj.ToString());
                    string sqlstr = @$"INSERT INTO Sys_FormDesignOptions(TITLE,FORMREVISION,FORMOPTIONS,FORMSTATUS,CREATEID,CREATOR,CREATEDATE)
                                        SELECT :title,:formReVision,FORMOPTIONS,:formStatus,:createid,:creator,:createdate
                                        FROM Sys_FormDesignOptions t
                                        WHERE t.TITLE=:sourceTitle AND t.FORMREVISION=:sourceRevision";
                    try
                    {
                        DBServerProvider.SqlDapper.ExcuteNonQuery(sqlstr, new
                        {
                            title = name,
                            formReVision = revision,
                            formStatus = 0,
                            createdate = DateTime.Now,
                            createid = UserContext.Current.UserId,
                            creator = UserContext.Current.UserTrueName,
                            sourceTitle = sourceEntity.name,
                            sourceRevision = sourceEntity.revision
                        });
                        return Response.OK(ResponseType.SaveSuccess);
                    }
                    catch (Exception ex)
                    {
                        return Response.Error(ex.Message);
                    }
                }
                else
                {
                    //NAME,REVISION不允许为空
                    throw new Exception(LanguageManager.Translate("common.shouldNotBeEmpty", LanguageManager.Translate("CamstarBaseObject.targetInstanceName") + "," + LanguageManager.Translate("CamstarBaseObject.targetInstanceRev")));
                }
            }
            else
            {
                throw new Exception("源对象不能为空");
            }
        }

        /// <summary>
        /// 检查表单名及版本时否已经存在
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public WebResponseContent CheckExist(Sys_FormDesignOptions entity)
        {
            WebResponseContent responseData = new WebResponseContent();

            if (repository.Exists(c => c.TITLE == entity.TITLE && c.FORMREVISION == entity.FORMREVISION))
            {
                return responseData.Error(LanguageManager.Translate("FormDesign.DuplicateNameOrVersion"));
            }
            else
            {
                return responseData.OK();
            }
        }

        public override WebResponseContent Add(SaveModel saveModel)
        {
            AddOnExecute = (SaveModel model) =>
            {
                Sys_FormDesignOptions mainEntity = saveModel.MainData.DicToEntity<Sys_FormDesignOptions>();

                if (_repository.Exists(x => x.TITLE == mainEntity.TITLE && x.FORMREVISION == mainEntity.FORMREVISION))
                {
                    return new WebResponseContent().Error(LanguageManager.Translate("FormDesign.DuplicateNameOrVersion"));
                }

                return new WebResponseContent().OK();
            };

            return base.Add(saveModel);
        }

        /// <summary>
        /// 发布
        /// </summary>
        /// <param name="saveModel"></param>
        /// <returns></returns>
        public virtual WebResponseContent FormPublish(SaveModel saveModel)
        {
            var entity = _repository.FindFirst(x => x.FORMID == int.Parse(saveModel.MainData["FORMID"].ToString()));
            if (entity == null)
            {
                return new WebResponseContent().Error("未找到该表单！");
            }
            entity.FORMSTATUS = 1;
            _repository.Update(entity, true);
            return new WebResponseContent().OK(ResponseType.SaveSuccess);
        }

        public WebResponseContent GetDataTableDataSource()
        {
            //1.如果备注中有添加JSON，则JSON中定义的key作为数据表格中的列信息；
            //2.如果备注中未设置列JSON，则查看数据源Value中第一个斜杠前的字符串作为表名查询SYS_TABLEINFO，获取列信息；
            //3.如果1和2均无法获取列信息，则尝试查询一次该api，返回结果的第一行的json作为数据表格中的列信息；(前端实现）
            //4.从tablInfo表中直接获取


            var dicList = DictionaryManager.GetDictionary("DATA_TABLE_DATASOURCEURL")?.Sys_DictionaryList?.Where(c => c.Enable == 1)?.ToList() ?? new List<Sys_DictionaryList>();
            //获取parentId!=0的全部表
            var extList = repository.DbContext.Set<Sys_TableInfo>().Where(x => x.parentId != 0).ToList();
            //dicList扩展，增加extList
            dicList.AddRange(extList.Select(x => new Sys_DictionaryList
            {
                DicName = "查询" + LanguageManager.Translate(x.columnCNName),
                DicValue = x.tableName + "/getPageData" + (x.camstarCdoType.HasValue && new int[] { (int)CamstarCdoType.NamedObject, (int)CamstarCdoType.RevisionedObject, (int)CamstarCdoType.SubEntityList, (int)CamstarCdoType.NamedSubEntityList }.Contains(x.camstarCdoType.Value) ? "Dynamic" : ""),
            }).Where(x => !dicList.Select(d => d.DicValue).Contains(x.DicValue)));

            List<dynamic> resList = new List<dynamic>();
            dicList.ForEach(dic =>
            {
                bool loadTreeData = false;
                if (string.IsNullOrWhiteSpace(dic.Remark))
                {
                    Sys_TableInfo tableInfo = repository.DbContext.Set<Sys_TableInfo>()
                                    .Where(x => x.tableName == dic.DicValue.Substring(0, dic.DicValue.IndexOf('/')))
                                    .Include(c => c.tableColumns)
                                    .FirstOrDefault();
                    loadTreeData = tableInfo?.camstarCdoType == (int)CamstarCdoType.RevisionedObject;
                    if (tableInfo?.tableColumns?.Count > 0)
                    {
                        ExpandoObject exp = formatTableColumns(tableInfo?.tableColumns)
                        .ToList().List2AnonymousObj(x => x.name);
                        dic.Remark = Newtonsoft.Json.JsonConvert.SerializeObject(exp);
                    }
                }
                resList.Add(new
                {
                    dataSource = dic.DicValue,
                    dataSourceName = dic.DicName,
                    columns = dic.Remark,
                    resultPath = "data.rows",
                    loadTreeData = loadTreeData
                });
            });

            return new WebResponseContent() { data = resList }.OK(ResponseType.QuerySuccess);
        }

        /// <summary>
        /// 从数据库中获取表对象+列信息
        /// </summary>
        /// <returns></returns>
        public WebResponseContent GetDataBaseEntities()
        {
            List<entityModel> resList = new List<entityModel>();

            List<Sys_TableInfo> tableInfoList = repository.DbContext.Set<Sys_TableInfo>()
                                    .Include(c => c.tableColumns)
                 .Include(c => c.detailTables)
                 .ThenInclude(d => d.DETAILTABLE_TABLEINFO)
                 .Select(c => new Sys_TableInfo
                 {
                     table_Id = c.table_Id,
                     cnName = c.cnName,
                     columnCNName = c.columnCNName,
                     enable = c.enable,
                     expressField = c.expressField,
                     folderName = c.folderName,
                     nameSpace = c.nameSpace,
                     parentId = c.parentId,
                     tableName = c.tableName,
                     tableTrueName = c.tableTrueName,
                     camstarCdoType = c.camstarCdoType,
                     tableColumns = c.tableColumns,
                     detailTables = c.detailTables.Select(d => new Sys_DetailTable
                     {
                         DETAILTABLEID = d.DETAILTABLEID,
                         DETAILCNNAME = d.DETAILCNNAME,
                         DETAILCDONAME = d.DETAILCDONAME,
                         DETAILTABLE_TABLE_ID = d.DETAILTABLE_TABLE_ID,
                         ORDERNO = d.ORDERNO,
                         FOREIGNKEY = d.FOREIGNKEY,
                         ISREADONLY = d.ISREADONLY,
                         table_Id = d.table_Id,
                         DETAILTABLE_TABLEINFO = new Sys_TableInfo
                         {
                             table_Id = d.DETAILTABLE_TABLEINFO.table_Id,
                             tableName = d.DETAILTABLE_TABLEINFO.tableName,
                             tableTrueName = d.DETAILTABLE_TABLEINFO.tableTrueName,
                             cnName = d.DETAILTABLE_TABLEINFO.cnName,
                             columnCNName = d.DETAILTABLE_TABLEINFO.columnCNName,
                         }
                     }).OrderBy(x => x.ORDERNO).ToList(),
                 })
                                   .ToList();

            tableInfoList.ForEach(tableInfo =>
            {
                entityModel res = new entityModel
                {
                    name = tableInfo.tableName,
                    label = tableInfo.columnCNName
                };
                if (tableInfo.detailTables != null)
                {
                    res.details = tableInfo.detailTables.Select(c => new detailTableModel { name = c.DETAILCDONAME, label = c.DETAILCDONAME, tableName = c.DETAILTABLE_TABLEINFO.tableName, foreignKey = c.FOREIGNKEY, isreadonly = c.ISREADONLY }).ToList();
                }

                if (tableInfo?.tableColumns?.Count > 0)
                {
                    var columnList = formatTableColumns(tableInfo.tableColumns).ToList();
                    res.columns = columnList;
                }
                resList.Add(res);
            });

            return new WebResponseContent() { data = resList }.OK(ResponseType.QuerySuccess);
        }

        private IEnumerable<tableColumnModel> formatTableColumns(List<Sys_TableColumn> tableColumns)
        {
            if (tableColumns?.Count > 0)
            {
                return tableColumns.Where(c => c.isDisplay == 1 || (c.editRowNo > 0 && c.editColNo > 0)).OrderBy(c => c.orderNo)
                     .Select(a => new tableColumnModel
                     {
                         name = a.columnName,
                         label = a.columnCnName,
                         type = a.editType switch
                         {
                             "selectList" => "select",
                             "cascaderList" => "cascader",
                             "file" => "file-upload",
                             "img" => "picture-upload",
                             "decimal" => "number",
                             _ => a.editType
                         },
                         precision = a.editType switch
                         {
                             "decimal" => 2,
                             _ => 0
                         },
                         multiple = a.editType switch
                         {
                             "selectList" => true,
                             "cascaderList" => true,
                             _ => false
                         },
                         dataSource = a.dropNo,
                         lazy = a.lazy == 1,
                         remote = a.remote == 1,
                         trigger = a.cdoName,
                         columnFiltering = a.filterable == 1,
                         columnSorting = a.sortable == 1,
                         columnSortingType = a.isZhCnOrder == 1 ? "zhCn" : "",
                         columnWidth = a.columnWidth,
                         columnType = a.columnType,
                         required = a.isNull == 0,
                         maxLength = a.maxlength,
                         //1.必须是cascader
                         //2.ID结尾，但非BASEID结尾
                         //3.如果tableColumns中存在对应BASEID，则为true,否则为false
                         //emitPath = new List<string> { "cascader", "cascaderList" }.Contains(a.editType) && (a.columnName.EndsWith("ID") && !a.columnName.EndsWith("BASEID") && tableColumns.Any(tc => tc.columnName == a.columnName.Insert(a.columnName.Length - 2, "BASE")) ? true : false,
                         emitPath = true,
                         isDisplay = a.isDisplay,
                         colSize = a.colSize * 2,
                         editRowNo = a.editRowNo,
                         editColNo = a.editColNo,
                         isReadonly = a.isReadDataset == 1
                     });
            }
            else
            {
                return null;
            }
        }

        private class entityModel
        {
            public string name { get; set; }
            public string label { get; set; }
            public List<tableColumnModel> columns { get; set; }
            public List<detailTableModel> details { get; set; }
        }

        private class tableColumnModel
        {
            public string name { get; set; }
            public string label { get; internal set; }
            public string type { get; internal set; }
            public bool multiple { get; internal set; }
            public string dataSource { get; internal set; }
            public bool lazy { get; internal set; }
            public bool remote { get; internal set; }
            public string trigger { get; internal set; }
            public bool columnFiltering { get; internal set; }
            public bool columnSorting { get; internal set; }
            public string columnSortingType { get; internal set; }
            public int? columnWidth { get; internal set; }
            public string columnType { get; internal set; }
            public bool required { get; internal set; }
            public bool isReadonly { get; internal set; }
            public bool emitPath { get; internal set; }
            public int? maxLength { get; internal set; }
            public int precision { get; internal set; }
            public int? colSize { get; internal set; }
            public int? editRowNo { get; internal set; }
            public int? editColNo { get; internal set; }
            public int? isDisplay { get; internal set; }
        }

        private class detailTableModel
        {
            public string name { get; set; }
            public string label { get; set; }
            public string tableName { get; internal set; }
            public string foreignKey { get; internal set; }
            public int? isreadonly { get; internal set; }
        }
    }
}
