/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using ZT.Entity;
using ZT.Entity.SystemModels;
using ZT.Entity.DomainModels;
using ZT.Entity.AttributeManager;
using ZT.AppManager.DomainModels;


namespace ZT.AppManager.DomainModels
{
    [Entity(TableCnName = "router.roleWorkstation",TableName = "SYS_ROLE_WORKSTATIONCARD")]
    [Table("SYS_ROLE_WORKSTATIONCARD")]
    [SugarTable("SYS_ROLE_WORKSTATIONCARD")]
    public class Sys_Role_WorkstationCard : BaseEntity
    {
        /// <summary>
       ///SysRoleWorkstationCard.SYS_ROLE_WORKSTATIONCARD_ID
       /// </summary>
       [Key]
       [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
       [Display(Name ="SysRoleWorkstationCard.SYS_ROLE_WORKSTATIONCARD_ID")]
       [Column(TypeName="int")]
       [Required(AllowEmptyStrings=false)]
       public int SYS_ROLE_WORKSTATIONCARD_ID { get; set; }

       /// <summary>
       ///Role.RoleName
       /// </summary>
       [Display(Name ="Role.RoleName")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int ROLE_ID { get; set; }

       [Display(Name ="ROLE")]
       [ForeignKey("ROLE_ID")]
       [Navigate(NavigateType.OneToOne, nameof(Sys_Role_WorkstationCard.ROLE_ID))]
       public Sys_Role? ROLE { get; set; }
/// <summary>
       ///
       /// </summary>
       [Display(Name ="SYS_WORKSTATIONCARD_ID")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int SYS_WORKSTATIONCARD_ID { get; set; }

       [Display(Name ="SYS_WORKSTATIONCARD")]
       [ForeignKey("SYS_WORKSTATIONCARD_ID")]
       [Navigate(NavigateType.OneToOne, nameof(Sys_Role_WorkstationCard.SYS_WORKSTATIONCARD_ID))]
       public Sys_WorkstationCard? SYS_WORKSTATIONCARD { get; set; }
/// <summary>
       ///SysRoleWorkstationCard.TITLE
       /// </summary>
       [Display(Name ="SysRoleWorkstationCard.TITLE")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string TITLE { get; set; }

       /// <summary>
       ///SysWorkstationCard.PAGESIZE
       /// </summary>
       [Display(Name ="SysWorkstationCard.PAGESIZE")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? PAGESIZE { get; set; }

       /// <summary>
       ///SysRoleWorkstationCard.DEFAULTTAB
       /// </summary>
       [Display(Name ="SysRoleWorkstationCard.DEFAULTTAB")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       [Editable(true)]
       public string? DEFAULTTAB { get; set; }

       /// <summary>
       ///SysRoleWorkstationCard.POSITION_Y
       /// </summary>
       [Display(Name ="SysRoleWorkstationCard.POSITION_Y")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? POSITION_Y { get; set; }

       /// <summary>
       ///SysRoleWorkstationCard.POSITION_X
       /// </summary>
       [Display(Name ="SysRoleWorkstationCard.POSITION_X")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? POSITION_X { get; set; }

       /// <summary>
       ///SysRoleWorkstationCard.WIDTH
       /// </summary>
       [Display(Name ="SysRoleWorkstationCard.WIDTH")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? WIDTH { get; set; }

       /// <summary>
       ///SysRoleWorkstationCard.HEIGHT
       /// </summary>
       [Display(Name ="SysRoleWorkstationCard.HEIGHT")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? HEIGHT { get; set; }

       /// <summary>
       ///SysRoleWorkstationCard.COLOR
       /// </summary>
       [Display(Name ="SysRoleWorkstationCard.COLOR")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       [Editable(true)]
       public string? COLOR { get; set; }

       /// <summary>
       ///common.creator
       /// </summary>
       [Display(Name ="common.creator")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       public string? CREATOR { get; set; }

       /// <summary>
       ///common.createDate
       /// </summary>
       [Display(Name ="common.createDate")]
       [Column(TypeName="datetime")]
       public DateTime? CREATEDATE { get; set; }

       /// <summary>
       ///common.modifier
       /// </summary>
       [Display(Name ="common.modifier")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       public string? MODIFIER { get; set; }

       /// <summary>
       ///common.modifyDate
       /// </summary>
       [Display(Name ="common.modifyDate")]
       [Column(TypeName="datetime")]
       public DateTime? MODIFYDATE { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="DEFAULTTAB_DICNO_KEYVALUE")]
       [NotMapped]
        [SugarColumn(IsIgnore =true)]
       public object? DEFAULTTAB_DICNO_KEYVALUE { get; set; }

       
    }
}