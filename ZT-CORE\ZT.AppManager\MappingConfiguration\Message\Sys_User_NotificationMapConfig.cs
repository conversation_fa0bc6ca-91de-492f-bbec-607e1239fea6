using ZT.Entity.MappingConfiguration;
using ZT.AppManager.DomainModels;
//using {ParentTableNamespace}.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.AppManager.MappingConfiguration
{
    public class Sys_User_NotificationMapConfig : EntityMappingConfiguration<Sys_User_Notification>
    {
    public override void Map(EntityTypeBuilder<Sys_User_Notification>
        builderTable)
        {
           //builderTable.HasOne<{ParentTableName}>(o => o.{TableTrueName}).WithOne().HasForeignKey<{ParentTableName}>(e => e.{Key});
           //builderTable.Navigation(o => o.{TableTrueName}).IsRequired();
        }
     }
}

