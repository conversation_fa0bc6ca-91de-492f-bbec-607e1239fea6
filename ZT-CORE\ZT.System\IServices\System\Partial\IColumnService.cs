/*
*所有关于MfgOrder类的业务代码接口应在此处编写
*/
using ZT.Core.BaseProvider;
using ZT.Entity.DomainModels;
using ZT.Core.Utilities;
using System.Linq.Expressions;
namespace ZT.System.IServices
{
    public partial interface IColumnService
    {
        public WebResponseContent Save(ZT.Entity.DomainModels.Kanban.ApiColumn apiColumn);
        public WebResponseContent Read(ZT.Entity.DomainModels.Kanban.ApiColumn apiColumn);
        public WebResponseContent Delete(ZT.Entity.DomainModels.Kanban.ApiColumn apiColumn);
    }
 }
