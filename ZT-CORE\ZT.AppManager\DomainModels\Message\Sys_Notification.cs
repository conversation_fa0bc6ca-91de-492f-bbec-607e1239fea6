/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using ZT.Entity;
using ZT.Entity.SystemModels;
using ZT.Entity.DomainModels;
using ZT.Entity.AttributeManager;
using ZT.AppManager.DomainModels;


namespace ZT.AppManager.DomainModels
{
    [Entity(TableCnName = "Notification.NotificationManagement",TableName = "SYS_NOTIFICATION")]
    [Table("SYS_NOTIFICATION")]
    [SugarTable("SYS_NOTIFICATION")]
    public class Sys_Notification : BaseEntity
    {
        /// <summary>
       ///
       /// </summary>
       [Key]
       [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
       [Display(Name ="NOTIFICATION_ID")]
       [Column(TypeName="int")]
       [Required(AllowEmptyStrings=false)]
       public int NOTIFICATION_ID { get; set; }

       /// <summary>
       ///Notification.Content
       /// </summary>
       [Display(Name ="Notification.Content")]
       [MaxLength(500)]
       [Column(TypeName="varchar(500)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string CONTENT { get; set; }

       /// <summary>
       ///Notification.LinkView
       /// </summary>
       [Display(Name ="Notification.LinkView")]
       [MaxLength(200)]
       [Column(TypeName="varchar(200)")]
       [Editable(true)]
       public string? LINKVIEW { get; set; }

       /// <summary>
       ///SysNotification.QUERYPARAM
       /// </summary>
       [Display(Name ="SysNotification.QUERYPARAM")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       [Editable(true)]
       public string? QUERYPARAM { get; set; }

       /// <summary>
       ///PriorityCode.Name
       /// </summary>
       [Display(Name ="PriorityCode.Name")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int PRIORITY { get; set; }

       /// <summary>
       ///Notification.ToEmployee
       /// </summary>
       [Display(Name ="Notification.ToEmployee")]
       [MaxLength(1000)]
       [Column(TypeName="varchar(1000)")]
       [Editable(true)]
       public string? TOEMPLOYEEID { get; set; }

       /// <summary>
       ///Notification.ToRole
       /// </summary>
       [Display(Name ="Notification.ToRole")]
       [MaxLength(1000)]
       [Column(TypeName="varchar(1000)")]
       [Editable(true)]
       public string? TOROLEID { get; set; }

       /// <summary>
       ///Notification.ToOperation
       /// </summary>
       [Display(Name ="Notification.ToOperation")]
       [MaxLength(1000)]
       [Column(TypeName="varchar(1000)")]
       [Editable(true)]
       public string? TOOPERATIONID { get; set; }

       /// <summary>
       ///Notification.ToWorkcenter
       /// </summary>
       [Display(Name ="Notification.ToWorkcenter")]
       [MaxLength(1000)]
       [Column(TypeName="varchar(1000)")]
       [Editable(true)]
       public string? TOWORKCENTERID { get; set; }

       /// <summary>
       ///Notification.ToFactory
       /// </summary>
       [Display(Name ="Notification.ToFactory")]
       [MaxLength(1000)]
       [Column(TypeName="varchar(1000)")]
       [Editable(true)]
       public string? TOFACTORYID { get; set; }

       /// <summary>
       ///SysEmail.TOFILTERTAG
       /// </summary>
       [Display(Name ="SysEmail.TOFILTERTAG")]
       [MaxLength(100)]
       [Column(TypeName="varchar(100)")]
       [Editable(true)]
       public string? TOFILTERTAG { get; set; }

       /// <summary>
       ///common.creator
       /// </summary>
       [Display(Name ="common.creator")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       public string? CREATOR { get; set; }

       /// <summary>
       ///common.createDate
       /// </summary>
       [Display(Name ="common.createDate")]
       [Column(TypeName="datetime")]
       public DateTime? CREATEDATE { get; set; }

       /// <summary>
       ///common.modifier
       /// </summary>
       [Display(Name ="common.modifier")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       public string? MODIFIER { get; set; }

       /// <summary>
       ///common.modifyDate
       /// </summary>
       [Display(Name ="common.modifyDate")]
       [Column(TypeName="datetime")]
       public DateTime? MODIFYDATE { get; set; }

       
    }
}