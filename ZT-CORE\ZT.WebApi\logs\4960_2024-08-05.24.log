2024-08-05 09:55:15.6325||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-3dc063922e2995dbe394abe0e70db442-c53bb4302ab667a1-00 ||end
2024-08-05 09:55:15.6593||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-43b717a279d4d146ee112bbc7f1e1c01-20ce33357dd4f851-00 ||end
2024-08-05 09:55:15.6856||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (27ms) [Parameters=[@__get_Item_Value_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[FORMID], [s].[FORMOPTIONS]
FROM [Sys_FormDesignOptions] AS [s]
WHERE [s].[FORMID] = @__get_Item_Value_0|| || 00-3dc063922e2995dbe394abe0e70db442-c53bb4302ab667a1-00 ||end
2024-08-05 09:55:15.6856||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-3a62af9afde2457fb3e2e892481c8cee-1c47995da5bad21f-00 ||end
2024-08-05 09:55:15.7192||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (31ms) [Parameters=[@__get_Item_Value_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[FORMID], [s].[FORMOPTIONS]
FROM [Sys_FormDesignOptions] AS [s]
WHERE [s].[FORMID] = @__get_Item_Value_0|| || 00-3a62af9afde2457fb3e2e892481c8cee-1c47995da5bad21f-00 ||end
2024-08-05 09:55:15.7192||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-f578228b1d1c9596b39b213171377b8c-0e0e99f4de0bdd7d-00 ||end
2024-08-05 09:55:15.7192||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-3dc063922e2995dbe394abe0e70db442-c53bb4302ab667a1-00 ||end
2024-08-05 09:55:15.7192||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_FormCollectionController.GetFormOption (ZT.WebApi)|| || 00-3dc063922e2995dbe394abe0e70db442-c53bb4302ab667a1-00 ||end
2024-08-05 09:55:15.7192||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"formId":10141}|| || 00-3dc063922e2995dbe394abe0e70db442-c53bb4302ab667a1-00 ||end
2024-08-05 09:55:15.7192||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":null,"message":null,"data":{"FORMID":10141,"FORMOPTIONS":"{\r\n  \"widgetList\": [\r\n    {\r\n      \"key\": 89673,\r\n      \"type\": \"button\",\r\n      \"icon\": \"svg-icon:button\",\r\n      \"formItemFlag\": false,\r\n      \"options\": {\r\n        \"name\": \"btnCopy\",\r\n        \"label\": \"common.copy\",\r\n        \"labelAlign\": \"\",\r\n        \"columnWidth\": null,\r\n        \"size\": \"small\",\r\n        \"displayStyle\": \"inline-flex\",\r\n        \"disabled\": false,\r\n        \"hidden\": false,\r\n        \"type\": \"primary\",\r\n        \"text\": true,\r\n        \"plain\": false,\r\n        \"round\": false,\r\n        \"circle\": false,\r\n        \"customClass\": [],\r\n        \"labelFontFamily\": \"\",\r\n        \"labelFontSize\": \"\",\r\n        \"labelBold\": false,\r\n        \"labelItalic\": false,\r\n        \"labelUnderline\": false,\r\n        \"labelLineThrough\": false,\r\n        \"labelIconClass\": \"CopyDocument\",\r\n        \"labelIconPosition\": \"front\",\r\n        \"labelTooltip\": null,\r\n        \"labelIconType\": \"pl\",\r\n        \"onCreated\": \"\",\r\n        \"onMounted\": \"const parentRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nthis.setHidden(!parentRef.checkButtonPermiss('Copy'));\",\r\n        \"onClick\": \"const parentRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nconst rows = (parentRef.getWidgetRef(parentRef.tableInfo.tableName)).getSelectionRows();\\r\\nif (rows.length !== 1) {\\r\\n  return parentRef.useMessage.error(this.$t('common.selectNoData', [this.$t('common.copy')]));\\r\\n}\\r\\nlet data = undefined;\\r\\nif (!!rows[0].OBJECTTOCHANGE) {\\r\\n  const _tmp = rows[0].OBJECTTOCHANGE;\\r\\n  data = {\\r\\n    OBJECTTOCHANGE: _tmp,\\r\\n    'OBJECTTOCHANGE.name': _tmp.name,\\r\\n    'OBJECTTOCHANGE.revision': _tmp.revision,\\r\\n    NEWNAME: _tmp.name,\\r\\n    NEWREVISION: _tmp.revision\\r\\n  };\\r\\n} else if (!!parentRef.tableInfo.expressField && !!rows[0][parentRef.tableInfo.expressField]) {\\r\\n  const _tmp = { id: rows[0][parentRef.tableInfo.tableKey], name: rows[0][parentRef.tableInfo.expressField] }\\r\\n  data = {\\r\\n    OBJECTTOCHANGE: _tmp,\\r\\n    'OBJECTTOCHANGE.name': _tmp.name,\\r\\n    'OBJECTTOCHANGE.revision': _tmp.revision,\\r\\n    NEWNAME: _tmp.name,\\r\\n    NEWREVISION: _tmp.revision,\\r\\n    expressField: parentRef.tableInfo.expressField\\r\\n  };\\r\\n} else {\\r\\n  return parentRef.useMessage.error(\\\"OBJECTTOCHANGE或expressField为空，不可执行复制操作。\\\");\\r\\n}\\r\\n(this.getWidgetRef('dialog21750')).open();\\r\\n(this.getWidgetRef('dialog21750')).setValue(data);\\r\\n\"\r\n      },\r\n      \"id\": \"button88910\"\r\n    },\r\n    {\r\n      \"key\": 85931,\r\n      \"type\": \"dialog\",\r\n      \"category\": \"container\",\r\n      \"icon\": \"svg-icon:dialog\",\r\n      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n      \"widgetList\": [\r\n        {\r\n          \"type\": \"dialog-header\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"static-text\",\r\n              \"icon\": \"svg-icon:static-text\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"statictext55902\",\r\n                \"label\": \"static-text\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 100,\r\n                \"hidden\": false,\r\n                \"textContent\": \"复制\",\r\n                \"displayStyle\": \"block\",\r\n                \"whiteSpace\": \"normal\",\r\n                \"columnFiltering\": false,\r\n                \"columnSorting\": false,\r\n                \"columnSortingType\": \"\",\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"MicrosoftYahei\",\r\n                \"labelFontSize\": \"!text-18.7px\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"InfoFilled\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"const rootRef = this.getFormRef().parentFormRef.parentFormRef.parentFormRef;\\r\\nthis.field.options.textContent = this.$t(rootRef.tableInfo.columnCNName) + '--' + this.$t('common.copy');\"\r\n              },\r\n              \"id\": \"statictext55902\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogheader34079\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": []\r\n            },\r\n            \"label\": \"dialog-header\"\r\n          },\r\n          \"id\": \"dialogheader34079\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-footer\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button54832\",\r\n                \"label\": \"common.cancel\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"info\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialog21750')?.handleClose();\"\r\n              },\r\n              \"id\": \"button54832\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button81767\",\r\n                \"label\": \"common.copy\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"primary\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"\\r\\n  const parentRef= this.getFormRef().parentFormRef;\\r\\n  parentRef.getWidgetRef('dialog21750')?.submit();\\r\\n\"\r\n              },\r\n              \"id\": \"button81767\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogfooter98914\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"small\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": [\r\n                \"flex\",\r\n                \"justify-end\"\r\n              ]\r\n            },\r\n            \"label\": \"dialog-footer\"\r\n          },\r\n          \"id\": \"dialogfooter98914\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-body\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"key\": 65147,\r\n              \"type\": \"table\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"svg-icon:table\",\r\n              \"rows\": [\r\n                {\r\n                  \"type\": \"table-row\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"cols\": [\r\n                    {\r\n                      \"type\": \"table-cell\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"table-cell\",\r\n                      \"internal\": true,\r\n                      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 17663,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"OBJECTTOCHANGE.name\",\r\n                            \"label\": \"TMESBaseObject.sourceInstanceName\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 100,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": true,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input13185\"\r\n                        }\r\n                      ],\r\n                      \"merged\": false,\r\n                      \"options\": {\r\n                        \"name\": \"tablecell86094\",\r\n                        \"cellWidth\": \"\",\r\n                        \"cellHeight\": \"\",\r\n                        \"verticalAlign\": \"middle\",\r\n                        \"horizontalAlign\": \"center\",\r\n                        \"displayStyle\": \"flex\",\r\n                        \"colspan\": 1,\r\n                        \"rowspan\": 1\r\n                      },\r\n                      \"id\": \"tablecell86094\"\r\n                    },\r\n                    {\r\n                      \"type\": \"table-cell\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"table-cell\",\r\n                      \"internal\": true,\r\n                      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 17663,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"NEWNAME\",\r\n                            \"label\": \"TMESBaseObject.targetInstanceName\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 100,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": true,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 30,\r\n                            \"showWordLimit\": true,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input98057\"\r\n                        }\r\n                      ],\r\n                      \"merged\": false,\r\n                      \"options\": {\r\n                        \"name\": \"tablecell59470\",\r\n                        \"cellWidth\": \"\",\r\n                        \"cellHeight\": \"\",\r\n                        \"verticalAlign\": \"middle\",\r\n                        \"horizontalAlign\": \"center\",\r\n                        \"displayStyle\": \"flex\",\r\n                        \"colspan\": 1,\r\n                        \"rowspan\": 1\r\n                      },\r\n                      \"id\": \"tablecell59470\"\r\n                    }\r\n                  ],\r\n                  \"merged\": false,\r\n                  \"id\": \"tablerow53778\"\r\n                },\r\n                {\r\n                  \"type\": \"table-row\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"cols\": [\r\n                    {\r\n                      \"type\": \"table-cell\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"table-cell\",\r\n                      \"internal\": true,\r\n                      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 17663,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"OBJECTTOCHANGE.revision\",\r\n                            \"label\": \"TMESBaseObject.sourceInstanceRev\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 100,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": true,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input37841\"\r\n                        }\r\n                      ],\r\n                      \"merged\": false,\r\n                      \"options\": {\r\n                        \"name\": \"tablecell98267\",\r\n                        \"cellWidth\": \"\",\r\n                        \"cellHeight\": \"\",\r\n                        \"verticalAlign\": \"middle\",\r\n                        \"horizontalAlign\": \"center\",\r\n                        \"displayStyle\": \"flex\",\r\n                        \"colspan\": 1,\r\n                        \"rowspan\": 1\r\n                      },\r\n                      \"id\": \"tablecell98267\"\r\n                    },\r\n                    {\r\n                      \"type\": \"table-cell\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"table-cell\",\r\n                      \"internal\": true,\r\n                      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 17663,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"NEWREVISION\",\r\n                            \"label\": \"TMESBaseObject.targetInstanceRev\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 100,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": true,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlank\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 15,\r\n                            \"showWordLimit\": true,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input103704\"\r\n                        }\r\n                      ],\r\n                      \"merged\": false,\r\n                      \"options\": {\r\n                        \"name\": \"tablecell70840\",\r\n                        \"cellWidth\": \"\",\r\n                        \"cellHeight\": \"\",\r\n                        \"verticalAlign\": \"middle\",\r\n                        \"horizontalAlign\": \"center\",\r\n                        \"displayStyle\": \"flex\",\r\n                        \"colspan\": 1,\r\n                        \"rowspan\": 1\r\n                      },\r\n                      \"id\": \"tablecell70840\"\r\n                    }\r\n                  ],\r\n                  \"merged\": false,\r\n                  \"id\": \"tablerow26083\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"table40092\",\r\n                \"hidden\": false,\r\n                \"tableCell\": true,\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"this.getFormRef().getFormData(false).then(formModel => {\\r\\n  if (!!!formModel.OBJECTTOCHANGE.revision) {\\r\\n    this.widget.rows.splice(1, 1)\\r\\n  }\\r\\n})\"\r\n              },\r\n              \"id\": \"table40092\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogbody76596\",\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-right-align\",\r\n              \"labelWidth\": 110,\r\n              \"customClass\": []\r\n            },\r\n            \"label\": \"dialog-body\",\r\n            \"columnHeight\": 160\r\n          },\r\n          \"id\": \"dialogbody76596\"\r\n        }\r\n      ],\r\n      \"options\": {\r\n        \"name\": \"dialog21750\",\r\n        \"label\": \"dialog\",\r\n        \"center\": false,\r\n        \"showClose\": true,\r\n        \"columnWidth\": 50,\r\n        \"draggable\": true,\r\n        \"top\": \"15vh\",\r\n        \"fullscreen\": true,\r\n        \"onOpenBefore\": \"\",\r\n        \"onOpenAfter\": \"\",\r\n        \"onSubmit\": \"const data = await this.getValue();\\r\\nconst formData = {\\r\\n  mainData: data\\r\\n};\\r\\nif(data['NEWNAME'] === data['OBJECTTOCHANGE.name'] && data['NEWREVISION']===data['OBJECTTOCHANGE.revision']){\\r\\n  useMessage.error(this.$t('common.alreadyExists',[data['NEWNAME']]));\\r\\n  return false;\\r\\n}\\r\\nconst rootRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nconst { http } = rootRef.commonApi();\\r\\nconst res = await http(rootRef.getUrl('copy2', true), 'post', { data: formData });\\r\\nif (!!res?.status) {\\r\\n  rootRef.useMessage.success(res.message);\\r\\n  //表格的重新加载\\r\\n  (rootRef.getWidgetRef(rootRef.tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false);\\r\\n} else {\\r\\n  return false;\\r\\n}\\r\\n\",\r\n        \"onCloseBefore\": \"\",\r\n        \"onCloseAfter\": \"const parentRef= this.getFormRef();\\r\\nparentRef.setFormData({'dialog21750':undefined});\"\r\n      },\r\n      \"id\": \"dialog21750\"\r\n    }\r\n  ],\r\n  \"formConfig\": {\r\n    \"name\": \"vForm33484\",\r\n    \"modelName\": \"formData\",\r\n    \"refName\": \"vForm\",\r\n    \"rulesName\": \"rules\",\r\n    \"labelWidth\": 80,\r\n    \"labelPosition\": \"left\",\r\n    \"size\": \"\",\r\n    \"labelAlign\": \"label-left-align\",\r\n    \"cssCode\": \"\",\r\n    \"customClass\": \"\",\r\n    \"functions\": \"\",\r\n    \"layoutType\": \"PC\",\r\n    \"jsonVersion\": 3,\r\n    \"disabled\": false,\r\n    \"readonly\": false,\r\n    \"entityName\": \"\",\r\n    \"dicNoList\": [],\r\n    \"onFormCreated\": \"\",\r\n    \"onFormMounted\": \"\",\r\n    \"onFormDataChange\": \"\",\r\n    \"labelFontFamily\": null,\r\n    \"labelFontSize\": null,\r\n    \"lazyDicNoList\": [],\r\n    \"optionItemsObject\": []\r\n  }\r\n}","FORMDATA":{}}}|| || 00-3dc063922e2995dbe394abe0e70db442-c53bb4302ab667a1-00 ||end
2024-08-05 09:55:15.7192||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (5ms) [Parameters=[@__get_Item_Value_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[FORMID], [s].[FORMOPTIONS]
FROM [Sys_FormDesignOptions] AS [s]
WHERE [s].[FORMID] = @__get_Item_Value_0|| || 00-43b717a279d4d146ee112bbc7f1e1c01-20ce33357dd4f851-00 ||end
2024-08-05 09:55:15.7192||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-3a62af9afde2457fb3e2e892481c8cee-1c47995da5bad21f-00 ||end
2024-08-05 09:55:15.7192||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_FormCollectionController.GetFormOption (ZT.WebApi)|| || 00-3a62af9afde2457fb3e2e892481c8cee-1c47995da5bad21f-00 ||end
2024-08-05 09:55:15.7192||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"formId":10244}|| || 00-3a62af9afde2457fb3e2e892481c8cee-1c47995da5bad21f-00 ||end
2024-08-05 09:55:15.7192||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":null,"message":null,"data":{"FORMID":10244,"FORMOPTIONS":"{\r\n  \"widgetList\": [\r\n    {\r\n      \"key\": 89673,\r\n      \"type\": \"button\",\r\n      \"icon\": \"svg-icon:button\",\r\n      \"formItemFlag\": false,\r\n      \"options\": {\r\n        \"name\": \"btnBindForm\",\r\n        \"label\": \"common.historical\",\r\n        \"labelAlign\": \"\",\r\n        \"columnWidth\": null,\r\n        \"size\": \"small\",\r\n        \"displayStyle\": \"inline-flex\",\r\n        \"disabled\": false,\r\n        \"hidden\": false,\r\n        \"type\": \"info\",\r\n        \"text\": true,\r\n        \"plain\": false,\r\n        \"round\": false,\r\n        \"circle\": false,\r\n        \"customClass\": [],\r\n        \"labelFontFamily\": \"\",\r\n        \"labelFontSize\": \"\",\r\n        \"labelBold\": false,\r\n        \"labelItalic\": false,\r\n        \"labelUnderline\": false,\r\n        \"labelLineThrough\": false,\r\n        \"labelIconClass\": \"List\",\r\n        \"labelIconPosition\": \"front\",\r\n        \"labelTooltip\": null,\r\n        \"labelIconType\": \"pl\",\r\n        \"onCreated\": \"\",\r\n        \"onMounted\": \"const parentRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nthis.setHidden(!parentRef.checkButtonPermiss('historicalTracing'));\",\r\n        \"onClick\": \"const parentRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nconst rows = (parentRef.getWidgetRef(parentRef.tableInfo.tableName)).getSelectionRows();\\r\\nif (rows.length !== 1) {\\r\\n  return parentRef.useMessage.error(this.$t('common.selectNoData', [this.$t('common.historical')]));\\r\\n}\\r\\n//获取自定义列名\\r\\nconst instanceid = !!this.historicalTracingBefore ? this.historicalTracingBefore() : undefined;\\r\\n//设置了列名但是找不到列\\r\\nif (instanceid && !rows[0][instanceid]) {\\r\\n  return parentRef.useMessage.error('未找到历史追溯配置的列，请检查')\\r\\n}\\r\\nconst data = {\\r\\n  TABLENAME: parentRef.tableInfo.tableName,\\r\\n  ID: instanceid ? rows[0][instanceid] : rows[0][parentRef.tableInfo.tableKey]\\r\\n};\\r\\n(this.getWidgetRef('dialogHistoricalTracing')).open();\\r\\n(this.getWidgetRef('dialogHistoricalTracing')).setValue(data);\"\r\n      },\r\n      \"id\": \"button88910\"\r\n    },\r\n    {\r\n      \"key\": 10095,\r\n      \"type\": \"dialog\",\r\n      \"category\": \"container\",\r\n      \"icon\": \"svg-icon:dialog\",\r\n      \"widgetList\": [\r\n        {\r\n          \"type\": \"dialog-header\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"static-text\",\r\n              \"icon\": \"svg-icon:static-text\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"statictext20355\",\r\n                \"label\": \"static-text\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 100,\r\n                \"hidden\": false,\r\n                \"textContent\": \"历史追溯\",\r\n                \"displayStyle\": \"block\",\r\n                \"whiteSpace\": \"normal\",\r\n                \"columnFiltering\": false,\r\n                \"columnSorting\": false,\r\n                \"columnSortingType\": \"\",\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"MicrosoftYahei\",\r\n                \"labelFontSize\": \"!text-18.7px\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"InfoFilled\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"const rootRef = this.getFormRef().parentFormRef.parentFormRef.parentFormRef;\\r\\nthis.field.options.textContent = this.$t(rootRef.tableInfo.columnCNName) + '--' + this.$t('common.historical');\"\r\n              },\r\n              \"id\": \"statictext20355\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogheader41011\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": \"\"\r\n            },\r\n            \"label\": \"dialog-header\"\r\n          },\r\n          \"id\": \"dialogheader41011\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-footer\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button43552\",\r\n                \"label\": \"common.close\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"info\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialogHistoricalTracing')?.handleClose();\"\r\n              },\r\n              \"id\": \"button43552\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogfooter19680\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": [\r\n                \"flex\",\r\n                \"justify-end\"\r\n              ]\r\n            },\r\n            \"label\": \"dialog-footer\"\r\n          },\r\n          \"id\": \"dialogfooter19680\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-body\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"key\": 74892,\r\n              \"type\": \"data-table\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"svg-icon:data-table\",\r\n              \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n              \"widgetList\": [\r\n                {\r\n                  \"key\": 37848,\r\n                  \"type\": \"select\",\r\n                  \"icon\": \"svg-icon:select-field\",\r\n                  \"formItemFlag\": true,\r\n                  \"options\": {\r\n                    \"name\": \"TABLE_ID\",\r\n                    \"label\": \"ModelingLog.objectName\",\r\n                    \"labelAlign\": \"\",\r\n                    \"defaultValue\": \"\",\r\n                    \"displayStyle\": \"block\",\r\n                    \"placeholder\": \"\",\r\n                    \"columnWidth\": 200,\r\n                    \"size\": \"\",\r\n                    \"labelWidth\": null,\r\n                    \"labelHidden\": false,\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"clearable\": true,\r\n                    \"filterable\": false,\r\n                    \"allowCreate\": false,\r\n                    \"remote\": false,\r\n                    \"multiple\": false,\r\n                    \"multipleLimit\": 0,\r\n                    \"lazy\": false,\r\n                    \"optionItems\": [\r\n                      {\r\n                        \"label\": \"select 1\",\r\n                        \"value\": \"1\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 2\",\r\n                        \"value\": \"2\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 3\",\r\n                        \"value\": \"3\"\r\n                      }\r\n                    ],\r\n                    \"required\": false,\r\n                    \"requiredHint\": \"\",\r\n                    \"dataSource\": \"SYS_TABLEINFO_TABLEID\",\r\n                    \"optionTagName\": \"key\",\r\n                    \"optionValueName\": \"value\",\r\n                    \"trigger\": \"\",\r\n                    \"formatter\": \"\",\r\n                    \"columnFixed\": false,\r\n                    \"columnFiltering\": false,\r\n                    \"columnSorting\": false,\r\n                    \"columnSortingType\": \"\",\r\n                    \"customClass\": [],\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\",\r\n                    \"onRemoteQuery\": \"\",\r\n                    \"onChange\": \"\",\r\n                    \"onFocus\": \"\",\r\n                    \"onBlur\": \"\",\r\n                    \"onValidate\": \"\"\r\n                  },\r\n                  \"id\": \"select16763\"\r\n                },\r\n                {\r\n                  \"key\": 20275,\r\n                  \"type\": \"input\",\r\n                  \"icon\": \"iconoir:input-field\",\r\n                  \"formItemFlag\": true,\r\n                  \"options\": {\r\n                    \"name\": \"OBJECTINSTANCEID\",\r\n                    \"label\": \"ModelingLog.objectTypeName\",\r\n                    \"labelAlign\": \"\",\r\n                    \"type\": \"text\",\r\n                    \"defaultValue\": \"\",\r\n                    \"displayStyle\": \"block\",\r\n                    \"placeholder\": \"\",\r\n                    \"columnWidth\": 200,\r\n                    \"size\": \"\",\r\n                    \"labelWidth\": null,\r\n                    \"labelHidden\": false,\r\n                    \"readonly\": false,\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"clearable\": true,\r\n                    \"showPassword\": false,\r\n                    \"required\": false,\r\n                    \"requiredHint\": \"\",\r\n                    \"validation\": [],\r\n                    \"validationHint\": \"\",\r\n                    \"columnFiltering\": false,\r\n                    \"columnSorting\": false,\r\n                    \"columnSortingType\": \"\",\r\n                    \"customClass\": [],\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"minLength\": 0,\r\n                    \"maxLength\": 100,\r\n                    \"showWordLimit\": false,\r\n                    \"prefixIcon\": \"\",\r\n                    \"suffixIcon\": \"\",\r\n                    \"appendButton\": false,\r\n                    \"appendButtonDisabled\": false,\r\n                    \"buttonIcon\": \"\",\r\n                    \"iconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\",\r\n                    \"onInput\": \"\",\r\n                    \"onChange\": \"\",\r\n                    \"onFocus\": \"\",\r\n                    \"onBlur\": \"\",\r\n                    \"onKeypressEnter\": \"\",\r\n                    \"onValidate\": \"\"\r\n                  },\r\n                  \"id\": \"input21545\"\r\n                },\r\n                {\r\n                  \"key\": 19004,\r\n                  \"type\": \"select\",\r\n                  \"icon\": \"svg-icon:select-field\",\r\n                  \"formItemFlag\": true,\r\n                  \"options\": {\r\n                    \"name\": \"EXECUTEACTION\",\r\n                    \"label\": \"ModelingLog.operate\",\r\n                    \"labelAlign\": \"\",\r\n                    \"defaultValue\": \"\",\r\n                    \"displayStyle\": \"block\",\r\n                    \"placeholder\": \"\",\r\n                    \"columnWidth\": 200,\r\n                    \"size\": \"\",\r\n                    \"labelWidth\": null,\r\n                    \"labelHidden\": false,\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"clearable\": true,\r\n                    \"filterable\": false,\r\n                    \"allowCreate\": false,\r\n                    \"remote\": false,\r\n                    \"multiple\": false,\r\n                    \"multipleLimit\": 0,\r\n                    \"lazy\": false,\r\n                    \"optionItems\": [\r\n                      {\r\n                        \"label\": \"select 1\",\r\n                        \"value\": \"1\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 2\",\r\n                        \"value\": \"2\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 3\",\r\n                        \"value\": \"3\"\r\n                      }\r\n                    ],\r\n                    \"required\": false,\r\n                    \"requiredHint\": \"\",\r\n                    \"dataSource\": \"MODELINGAUDITTRAIL_EXECUTEACTION\",\r\n                    \"optionTagName\": \"key\",\r\n                    \"optionValueName\": \"value\",\r\n                    \"trigger\": \"\",\r\n                    \"formatter\": \"\",\r\n                    \"columnFixed\": false,\r\n                    \"columnFiltering\": true,\r\n                    \"columnSorting\": false,\r\n                    \"columnSortingType\": \"\",\r\n                    \"customClass\": [],\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\",\r\n                    \"onRemoteQuery\": \"\",\r\n                    \"onChange\": \"\",\r\n                    \"onFocus\": \"\",\r\n                    \"onBlur\": \"\",\r\n                    \"onValidate\": \"\"\r\n                  },\r\n                  \"id\": \"select60589\"\r\n                },\r\n                {\r\n                  \"key\": 26801,\r\n                  \"type\": \"datetime\",\r\n                  \"icon\": \"svg-icon:datetime-field\",\r\n                  \"formItemFlag\": true,\r\n                  \"options\": {\r\n                    \"name\": \"TXNDATE\",\r\n                    \"label\": \"Container.Timers\",\r\n                    \"labelAlign\": \"\",\r\n                    \"type\": \"datetime\",\r\n                    \"defaultValue\": null,\r\n                    \"displayStyle\": \"block\",\r\n                    \"columnWidth\": 200,\r\n                    \"size\": \"\",\r\n                    \"labelWidth\": null,\r\n                    \"labelHidden\": false,\r\n                    \"readonly\": false,\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"clearable\": true,\r\n                    \"editable\": false,\r\n                    \"dateTimeFormat\": \"YYYY-MM-DD HH:mm:ss\",\r\n                    \"valueFormat\": \"YYYY-MM-DD HH:mm:ss\",\r\n                    \"required\": false,\r\n                    \"requiredHint\": \"\",\r\n                    \"columnFiltering\": true,\r\n                    \"columnSorting\": true,\r\n                    \"columnSortingType\": \"\",\r\n                    \"customClass\": [],\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\",\r\n                    \"onChange\": \"\",\r\n                    \"onFocus\": \"\",\r\n                    \"onBlur\": \"\",\r\n                    \"onValidate\": \"\"\r\n                  },\r\n                  \"id\": \"datetime39852\"\r\n                },\r\n                {\r\n                  \"key\": 37848,\r\n                  \"type\": \"select\",\r\n                  \"icon\": \"svg-icon:select-field\",\r\n                  \"formItemFlag\": true,\r\n                  \"options\": {\r\n                    \"name\": \"USER_ID\",\r\n                    \"label\": \"common.UserInfo\",\r\n                    \"labelAlign\": \"\",\r\n                    \"defaultValue\": \"\",\r\n                    \"displayStyle\": \"block\",\r\n                    \"placeholder\": \"\",\r\n                    \"columnWidth\": 200,\r\n                    \"size\": \"\",\r\n                    \"labelWidth\": null,\r\n                    \"labelHidden\": false,\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"clearable\": true,\r\n                    \"filterable\": false,\r\n                    \"allowCreate\": false,\r\n                    \"remote\": false,\r\n                    \"multiple\": false,\r\n                    \"multipleLimit\": 0,\r\n                    \"lazy\": false,\r\n                    \"optionItems\": [\r\n                      {\r\n                        \"label\": \"select 1\",\r\n                        \"value\": \"1\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 2\",\r\n                        \"value\": \"2\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 3\",\r\n                        \"value\": \"3\"\r\n                      }\r\n                    ],\r\n                    \"required\": false,\r\n                    \"requiredHint\": \"\",\r\n                    \"dataSource\": \"USER_USERTRUENAME\",\r\n                    \"optionTagName\": \"key\",\r\n                    \"optionValueName\": \"value\",\r\n                    \"trigger\": \"\",\r\n                    \"formatter\": \"\",\r\n                    \"columnFixed\": false,\r\n                    \"columnFiltering\": true,\r\n                    \"columnSorting\": false,\r\n                    \"columnSortingType\": \"\",\r\n                    \"customClass\": [],\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\",\r\n                    \"onRemoteQuery\": \"\",\r\n                    \"onChange\": \"\",\r\n                    \"onFocus\": \"\",\r\n                    \"onBlur\": \"\",\r\n                    \"onValidate\": \"\"\r\n                  },\r\n                  \"id\": \"select38064\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"dtHistoryHeader\",\r\n                \"hidden\": false,\r\n                \"rowSpacing\": 8,\r\n                \"height\": 180,\r\n                \"dataSourceUrl\": \"ModelingAuditTrail/getPageData\",\r\n                \"resultPath\": \"data.rows\",\r\n                \"disabled\": false,\r\n                \"readonly\": true,\r\n                \"onlyChangedData\": true,\r\n                \"loadTreeData\": false,\r\n                \"rowKey\": \"__row_key\",\r\n                \"childrenKey\": \"children\",\r\n                \"stripe\": true,\r\n                \"showIndex\": false,\r\n                \"showCheckBox\": false,\r\n                \"paging\": true,\r\n                \"smallPagination\": false,\r\n                \"border\": true,\r\n                \"size\": \"\",\r\n                \"pagination\": {\r\n                  \"currentPage\": 1,\r\n                  \"pageSizes\": [\r\n                    10,\r\n                    15,\r\n                    20,\r\n                    30,\r\n                    50,\r\n                    100,\r\n                    200\r\n                  ],\r\n                  \"pageSize\": 20,\r\n                  \"total\": 366,\r\n                  \"pagerCount\": 5\r\n                },\r\n                \"defaultValue\": [\r\n                  {\r\n                    \"__row_key\": \"__row_key_25791\",\r\n                    \"TXNDATE\": null,\r\n                    \"TABLE_ID\": \"\",\r\n                    \"OBJECTINSTANCEID\": \"\",\r\n                    \"USER_ID\": \"\",\r\n                    \"EXECUTEACTION\": \"\"\r\n                  }\r\n                ],\r\n                \"onLoadBefore\": \"const formData = this.formModel;\\r\\nif (!!formData['ID']) {\\r\\n  param.wheres.push(\\r\\n    ...[\\r\\n      { name: 'TABLENAME', value: formData['TABLENAME'], displayType: '=' },\\r\\n      { name: 'OBJECTINSTANCEID', value: formData['ID'], displayType: '=' }\\r\\n    ]\\r\\n  )\\r\\n  param.orderbys = !!!param.orderbys || param.orderbys.length === 0 ? [{ sort: \\\"TXNDATE\\\", order: \\\"desc\\\" }] : param.orderbys\\r\\n}\",\r\n                \"onPageSizeChange\": \"\",\r\n                \"onCurrentPageChange\": \"\",\r\n                \"onSelectionChange\": \"console.log(selection);\\r\\nif(selection[0] !== null){\\r\\n(this.getWidgetRef('dtHistoryDetail')).initTableData();\\r\\n}\\r\\n\",\r\n                \"onHideOperationButton\": \"\",\r\n                \"onDisableOperationButton\": \"\",\r\n                \"onGetOperationButtonLabel\": \"\",\r\n                \"onOperationButtonClick\": \"\",\r\n                \"onHeaderClick\": \"\",\r\n                \"onRowClick\": \"\",\r\n                \"onRowDoubleClick\": \"\",\r\n                \"onCellClick\": \"\",\r\n                \"onCellDoubleClick\": \"\",\r\n                \"onGetRowClassName\": \"\",\r\n                \"onGetSpanMethod\": \"\",\r\n                \"label\": \"data-table\"\r\n              },\r\n              \"id\": \"datatable45672\"\r\n            },\r\n            {\r\n              \"key\": 21536,\r\n              \"type\": \"collapse\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"svg-icon:collapse\",\r\n              \"items\": [\r\n                {\r\n                  \"type\": \"collapse-item\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"collapse-item\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 74892,\r\n                      \"type\": \"data-table\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"svg-icon:data-table\",\r\n                      \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 20275,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"AttributeType\",\r\n                            \"label\": \"ModelingLog.Item\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 200,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input98477\"\r\n                        },\r\n                        {\r\n                          \"key\": 20275,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"Attribute\",\r\n                            \"label\": \"ModelingLog.Name\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 200,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input41345\"\r\n                        },\r\n                        {\r\n                          \"key\": 20275,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"Before\",\r\n                            \"label\": \"ModelingLog.OldValue\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 200,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input95790\"\r\n                        },\r\n                        {\r\n                          \"key\": 20275,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"After\",\r\n                            \"label\": \"ModelingLog.NewValue\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 200,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input97329\"\r\n                        },\r\n                        {\r\n                          \"key\": 20275,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"ExcuteAction\",\r\n                            \"label\": \"ModelingLog.operate\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 200,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [],\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input77642\"\r\n                        }\r\n                      ],\r\n                      \"options\": {\r\n                        \"name\": \"dtHistoryDetail\",\r\n                        \"hidden\": false,\r\n                        \"rowSpacing\": 8,\r\n                        \"height\": 200,\r\n                        \"dataSourceUrl\": \"ModelingAuditTrail/GetDetailPage\",\r\n                        \"resultPath\": \"data.rows\",\r\n                        \"disabled\": false,\r\n                        \"readonly\": true,\r\n                        \"onlyChangedData\": true,\r\n                        \"loadTreeData\": true,\r\n                        \"rowKey\": \"__row_key\",\r\n                        \"childrenKey\": \"children\",\r\n                        \"stripe\": true,\r\n                        \"showIndex\": false,\r\n                        \"showCheckBox\": true,\r\n                        \"paging\": true,\r\n                        \"smallPagination\": false,\r\n                        \"border\": true,\r\n                        \"size\": \"\",\r\n                        \"pagination\": {\r\n                          \"currentPage\": 1,\r\n                          \"pageSizes\": [\r\n                            10,\r\n                            15,\r\n                            20,\r\n                            30,\r\n                            50,\r\n                            100,\r\n                            200\r\n                          ],\r\n                          \"pageSize\": 20,\r\n                          \"total\": 366,\r\n                          \"pagerCount\": 5\r\n                        },\r\n                        \"defaultValue\": [\r\n                          {\r\n                            \"__row_key\": \"__row_key_36584\",\r\n                            \"AttributeType\": \"\",\r\n                            \"Attribute\": \"\",\r\n                            \"Before\": \"\",\r\n                            \"After\": \"\",\r\n                            \"ExcuteAction\": \"\"\r\n                          }\r\n                        ],\r\n                        \"onLoadBefore\": \"const rows = this.getWidgetRef('dtHistoryHeader').getSelectionRows();\\r\\nconsole.log('rows',rows);\\r\\nif (rows === null || rows?.length !== 1) {\\r\\n  return false\\r\\n}\\r\\nparam.rows = 500;\\r\\n\\r\\nparam.wheres.push(\\r\\n  { name: 'MODELINGAUDITTRAILID', value: rows[0]['MODELINGAUDITTRAILID'], displayType: '=' }\\r\\n)\",\r\n                        \"onPageSizeChange\": \"\",\r\n                        \"onCurrentPageChange\": \"\",\r\n                        \"onSelectionChange\": \"\",\r\n                        \"onHideOperationButton\": \"\",\r\n                        \"onDisableOperationButton\": \"\",\r\n                        \"onGetOperationButtonLabel\": \"\",\r\n                        \"onOperationButtonClick\": \"\",\r\n                        \"onHeaderClick\": \"\",\r\n                        \"onRowClick\": \"\",\r\n                        \"onRowDoubleClick\": \"\",\r\n                        \"onCellClick\": \"\",\r\n                        \"onCellDoubleClick\": \"\",\r\n                        \"onGetRowClassName\": \"\",\r\n                        \"onGetSpanMethod\": \"\",\r\n                        \"label\": \"data-table\",\r\n                        \"onLoadAfter\": \"\"\r\n                      },\r\n                      \"id\": \"datatable26662\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"collapseitem20634\",\r\n                    \"label\": \"ModelingLog.modelingLogDetail\",\r\n                    \"disabled\": false\r\n                  },\r\n                  \"id\": \"collapseitem20634\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"collapse64432\",\r\n                \"accordion\": false,\r\n                \"defaultValue\": [\r\n                  \"collapseitem20634\"\r\n                ],\r\n                \"hidden\": false\r\n              },\r\n              \"id\": \"collapse64432\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogbody97557\",\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": []\r\n            },\r\n            \"label\": \"dialog-body\",\r\n            \"columnHeight\": null\r\n          },\r\n          \"id\": \"dialogbody97557\"\r\n        }\r\n      ],\r\n      \"options\": {\r\n        \"name\": \"dialogHistoricalTracing\",\r\n        \"label\": \"历史追溯\",\r\n        \"center\": false,\r\n        \"showClose\": true,\r\n        \"columnWidth\": 50,\r\n        \"draggable\": true,\r\n        \"top\": \"15vh\",\r\n        \"fullscreen\": true,\r\n        \"onOpenBefore\": \"\",\r\n        \"onOpenAfter\": \"\",\r\n        \"onSubmit\": \"\",\r\n        \"onCloseBefore\": \"\",\r\n        \"onCloseAfter\": \"\"\r\n      },\r\n      \"id\": \"dialog64348\"\r\n    }\r\n  ],\r\n  \"formConfig\": {\r\n    \"name\": \"vForm43866\",\r\n    \"modelName\": \"formData\",\r\n    \"refName\": \"vForm\",\r\n    \"rulesName\": \"rules\",\r\n    \"labelWidth\": 80,\r\n    \"labelPosition\": \"left\",\r\n    \"size\": \"\",\r\n    \"labelAlign\": \"label-left-align\",\r\n    \"cssCode\": \"\",\r\n    \"customClass\": \"\",\r\n    \"functions\": \"\",\r\n    \"layoutType\": \"PC\",\r\n    \"jsonVersion\": 3,\r\n    \"disabled\": false,\r\n    \"readonly\": false,\r\n    \"entityName\": \"\",\r\n    \"dicNoList\": [],\r\n    \"onFormCreated\": \"\",\r\n    \"onFormMounted\": \"\",\r\n    \"onFormDataChange\": \"\",\r\n    \"labelFontFamily\": null,\r\n    \"labelFontSize\": null,\r\n    \"lazyDicNoList\": [],\r\n    \"optionItemsObject\": []\r\n  }\r\n}","FORMDATA":{}}}|| || 00-3a62af9afde2457fb3e2e892481c8cee-1c47995da5bad21f-00 ||end
2024-08-05 09:55:15.7330||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (13ms) [Parameters=[@__get_Item_Value_0='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[FORMID], [s].[FORMOPTIONS]
FROM [Sys_FormDesignOptions] AS [s]
WHERE [s].[FORMID] = @__get_Item_Value_0|| || 00-f578228b1d1c9596b39b213171377b8c-0e0e99f4de0bdd7d-00 ||end
2024-08-05 09:55:15.7330||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-43b717a279d4d146ee112bbc7f1e1c01-20ce33357dd4f851-00 ||end
2024-08-05 09:55:15.7330||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_FormCollectionController.GetFormOption (ZT.WebApi)|| || 00-43b717a279d4d146ee112bbc7f1e1c01-20ce33357dd4f851-00 ||end
2024-08-05 09:55:15.7330||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"formId":10240}|| || 00-43b717a279d4d146ee112bbc7f1e1c01-20ce33357dd4f851-00 ||end
2024-08-05 09:55:15.7330||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":null,"message":null,"data":{"FORMID":10240,"FORMOPTIONS":"{\r\n  \"widgetList\": [\r\n    {\r\n      \"key\": 89673,\r\n      \"type\": \"button\",\r\n      \"icon\": \"svg-icon:button\",\r\n      \"formItemFlag\": false,\r\n      \"options\": {\r\n        \"name\": \"btnFreeze\",\r\n        \"label\": \"common.freeze\",\r\n        \"labelAlign\": \"\",\r\n        \"columnWidth\": null,\r\n        \"size\": \"small\",\r\n        \"displayStyle\": \"inline-flex\",\r\n        \"disabled\": false,\r\n        \"hidden\": false,\r\n        \"type\": \"danger\",\r\n        \"text\": true,\r\n        \"plain\": false,\r\n        \"round\": false,\r\n        \"circle\": false,\r\n        \"customClass\": [],\r\n        \"labelFontFamily\": \"\",\r\n        \"labelFontSize\": \"\",\r\n        \"labelBold\": false,\r\n        \"labelItalic\": false,\r\n        \"labelUnderline\": false,\r\n        \"labelLineThrough\": false,\r\n        \"labelIconClass\": \"Lock\",\r\n        \"labelIconPosition\": \"front\",\r\n        \"labelTooltip\": null,\r\n        \"labelIconType\": \"pl\",\r\n        \"onCreated\": \"\",\r\n        \"onMounted\": \"const parentRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nthis.setHidden(!parentRef.checkButtonPermiss('Freeze'));\",\r\n        \"onClick\": \"const parentRef= this.getFormRef().parentFormRef.parentFormRef;\\r\\nconst rows = (parentRef.getWidgetRef(parentRef.tableInfo.tableName)).getSelectionRows();\\r\\nif (rows.length !== 1) {\\r\\n  return parentRef.useMessage.error(this.$t('common.selectNoData', [this.$t('common.freeze')]));\\r\\n}\\r\\nconst data = {\\r\\n  OBJECTTOCHANGE:rows[0].OBJECTTOCHANGE,\\r\\n  'OBJECTTOCHANGE.name': rows[0].OBJECTTOCHANGE.name,\\r\\n  'OBJECTTOCHANGE.revision': rows[0].OBJECTTOCHANGE.revision,\\r\\n  ISFROZEN: rows[0].ISFROZEN,\\r\\n  TARGETFROZEN: 1-rows[0].ISFROZEN\\r\\n};\\r\\n(this.getWidgetRef('dialog21750')).open();\\r\\n(this.getWidgetRef('dialog21750')).setValue(data);\\r\\n\"\r\n      },\r\n      \"id\": \"button88910\"\r\n    },\r\n    {\r\n      \"key\": 85931,\r\n      \"type\": \"dialog\",\r\n      \"category\": \"container\",\r\n      \"icon\": \"svg-icon:dialog\",\r\n      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n      \"widgetList\": [\r\n        {\r\n          \"type\": \"dialog-header\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"static-text\",\r\n              \"icon\": \"svg-icon:static-text\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"statictext55902\",\r\n                \"label\": \"static-text\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 100,\r\n                \"hidden\": false,\r\n                \"textContent\": \"冻结\",\r\n                \"displayStyle\": \"block\",\r\n                \"whiteSpace\": \"normal\",\r\n                \"columnFiltering\": false,\r\n                \"columnSorting\": false,\r\n                \"columnSortingType\": \"\",\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"MicrosoftYahei\",\r\n                \"labelFontSize\": \"!text-18.7px\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"InfoFilled\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"const rootRef = this.getFormRef().parentFormRef.parentFormRef.parentFormRef;\\r\\nthis.field.options.textContent = this.$t(rootRef.tableInfo.columnCNName) + '--' + this.$t('common.copy');\"\r\n              },\r\n              \"id\": \"statictext55902\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogheader34079\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": \"\"\r\n            },\r\n            \"label\": \"dialog-header\"\r\n          },\r\n          \"id\": \"dialogheader34079\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-footer\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button54832\",\r\n                \"label\": \"common.cancel\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"info\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialog21750')?.handleClose();\"\r\n              },\r\n              \"id\": \"button54832\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button81767\",\r\n                \"label\": \"common.freeze\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"primary\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialog21750')?.submit();\"\r\n              },\r\n              \"id\": \"button81767\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogfooter98914\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": [\r\n                \"flex\",\r\n                \"justify-end\"\r\n              ]\r\n            },\r\n            \"label\": \"dialog-footer\"\r\n          },\r\n          \"id\": \"dialogfooter98914\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-body\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"key\": 34874,\r\n              \"type\": \"table\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"svg-icon:table\",\r\n              \"rows\": [\r\n                {\r\n                  \"type\": \"table-row\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"cols\": [\r\n                    {\r\n                      \"type\": \"table-cell\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"table-cell\",\r\n                      \"internal\": true,\r\n                      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 101753,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"OBJECTTOCHANGE.name\",\r\n                            \"label\": \"TMESBaseObject.InstanceName\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 100,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": true,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": \"\",\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input30616\"\r\n                        }\r\n                      ],\r\n                      \"merged\": false,\r\n                      \"options\": {\r\n                        \"name\": \"tablecell118896\",\r\n                        \"cellWidth\": \"\",\r\n                        \"cellHeight\": \"\",\r\n                        \"verticalAlign\": \"middle\",\r\n                        \"horizontalAlign\": \"center\",\r\n                        \"displayStyle\": \"flex\",\r\n                        \"colspan\": 1,\r\n                        \"rowspan\": 1\r\n                      },\r\n                      \"id\": \"tablecell118896\"\r\n                    },\r\n                    {\r\n                      \"type\": \"table-cell\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"table-cell\",\r\n                      \"internal\": true,\r\n                      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 32080,\r\n                          \"type\": \"switch\",\r\n                          \"icon\": \"svg-icon:switch-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"ISFROZEN\",\r\n                            \"label\": \"TMESBaseObject.SourceFreezeState\",\r\n                            \"labelAlign\": \"\",\r\n                            \"defaultValue\": 0,\r\n                            \"displayStyle\": \"block\",\r\n                            \"columnWidth\": 100,\r\n                            \"labelWidth\": \"90px\",\r\n                            \"labelHidden\": false,\r\n                            \"disabled\": true,\r\n                            \"hidden\": false,\r\n                            \"size\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"switchWidth\": 40,\r\n                            \"activeColor\": \"#F8AAAA\",\r\n                            \"inactiveColor\": \"#ACF4B9\",\r\n                            \"activeValue\": 1,\r\n                            \"inactiveValue\": 0,\r\n                            \"activeDisplayMode\": true,\r\n                            \"inactiveDisplayMode\": false,\r\n                            \"inlinePrompt\": true,\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onValidate\": \"\",\r\n                            \"activeText\": \"\",\r\n                            \"inactiveText\": \"\",\r\n                            \"activeIcon\": \"Lock\",\r\n                            \"inactiveIcon\": \"Unlock\"\r\n                          },\r\n                          \"id\": \"switch36233\"\r\n                        }\r\n                      ],\r\n                      \"merged\": false,\r\n                      \"options\": {\r\n                        \"name\": \"tablecell80417\",\r\n                        \"cellWidth\": \"\",\r\n                        \"cellHeight\": \"\",\r\n                        \"verticalAlign\": \"middle\",\r\n                        \"horizontalAlign\": \"center\",\r\n                        \"displayStyle\": \"flex\",\r\n                        \"colspan\": 1,\r\n                        \"rowspan\": 1\r\n                      },\r\n                      \"id\": \"tablecell80417\"\r\n                    }\r\n                  ],\r\n                  \"merged\": false,\r\n                  \"id\": \"tablerow49634\"\r\n                },\r\n                {\r\n                  \"type\": \"table-row\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"cols\": [\r\n                    {\r\n                      \"type\": \"table-cell\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"table-cell\",\r\n                      \"internal\": true,\r\n                      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 101753,\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"OBJECTTOCHANGE.revision\",\r\n                            \"label\": \"TMESBaseObject.InstanceRev\",\r\n                            \"labelAlign\": \"\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 100,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": true,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": \"\",\r\n                            \"validationHint\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 100,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"this.setHidden(!!!this.getValue());\\r\\n\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input71674\"\r\n                        }\r\n                      ],\r\n                      \"merged\": false,\r\n                      \"options\": {\r\n                        \"name\": \"tablecell98136\",\r\n                        \"cellWidth\": \"\",\r\n                        \"cellHeight\": \"\",\r\n                        \"verticalAlign\": \"middle\",\r\n                        \"horizontalAlign\": \"center\",\r\n                        \"displayStyle\": \"flex\",\r\n                        \"colspan\": 1,\r\n                        \"rowspan\": 1\r\n                      },\r\n                      \"id\": \"tablecell98136\"\r\n                    },\r\n                    {\r\n                      \"type\": \"table-cell\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"table-cell\",\r\n                      \"internal\": true,\r\n                      \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"key\": 32080,\r\n                          \"type\": \"switch\",\r\n                          \"icon\": \"svg-icon:switch-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"TARGETFROZEN\",\r\n                            \"label\": \"TMESBaseObject.TargetFreezeState\",\r\n                            \"labelAlign\": \"\",\r\n                            \"defaultValue\": 1,\r\n                            \"displayStyle\": \"block\",\r\n                            \"columnWidth\": 100,\r\n                            \"labelWidth\": \"90px\",\r\n                            \"labelHidden\": false,\r\n                            \"disabled\": true,\r\n                            \"hidden\": false,\r\n                            \"size\": \"\",\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": [],\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"switchWidth\": 40,\r\n                            \"activeColor\": \"#F8AAAA\",\r\n                            \"inactiveColor\": \"#ACF4B9\",\r\n                            \"activeValue\": 1,\r\n                            \"inactiveValue\": 0,\r\n                            \"activeDisplayMode\": true,\r\n                            \"inactiveDisplayMode\": false,\r\n                            \"inlinePrompt\": true,\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onValidate\": \"\",\r\n                            \"activeText\": \"\",\r\n                            \"inactiveText\": \"\",\r\n                            \"activeIcon\": \"Lock\",\r\n                            \"inactiveIcon\": \"Unlock\"\r\n                          },\r\n                          \"id\": \"switch50230\"\r\n                        }\r\n                      ],\r\n                      \"merged\": false,\r\n                      \"options\": {\r\n                        \"name\": \"tablecell15377\",\r\n                        \"cellWidth\": \"\",\r\n                        \"cellHeight\": \"\",\r\n                        \"verticalAlign\": \"middle\",\r\n                        \"horizontalAlign\": \"center\",\r\n                        \"displayStyle\": \"flex\",\r\n                        \"colspan\": 1,\r\n                        \"rowspan\": 1\r\n                      },\r\n                      \"id\": \"tablecell15377\"\r\n                    }\r\n                  ],\r\n                  \"merged\": false,\r\n                  \"id\": \"tablerow60102\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"table86575\",\r\n                \"hidden\": false,\r\n                \"tableCell\": true,\r\n                \"onMounted\": \"\"\r\n              },\r\n              \"id\": \"table86575\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogbody76596\",\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": []\r\n            },\r\n            \"label\": \"dialog-body\",\r\n            \"columnHeight\": 160\r\n          },\r\n          \"id\": \"dialogbody76596\"\r\n        }\r\n      ],\r\n      \"options\": {\r\n        \"name\": \"dialog21750\",\r\n        \"label\": \"dialog\",\r\n        \"center\": false,\r\n        \"showClose\": true,\r\n        \"columnWidth\": 50,\r\n        \"draggable\": true,\r\n        \"top\": \"15vh\",\r\n        \"fullscreen\": true,\r\n        \"onOpenBefore\": \"\",\r\n        \"onOpenAfter\": \"\",\r\n        \"onSubmit\": \"const data = await this.getValue();\\r\\nconst formData = {\\r\\n  mainData: data\\r\\n}\\r\\nconst rootRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nconst { http } = rootRef.commonApi();\\r\\nconst res = await http(rootRef.getUrl('freeze',true), 'post', { data: formData });\\r\\n  if (!!res?.status) {\\r\\n    rootRef.useMessage.success(res.message);\\r\\n    this.formModel[this.widget.options.name] = undefined;\\r\\n    //表格的重新加载\\r\\n  (rootRef.getWidgetRef(rootRef.tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false);\\r\\n} else {\\r\\n  return false;\\r\\n}\",\r\n        \"onCloseBefore\": \"\",\r\n        \"onCloseAfter\": \"const parentRef= this.getFormRef();\\r\\nparentRef.setFormData({'dialog21750':undefined});\"\r\n      },\r\n      \"id\": \"dialog21750\"\r\n    }\r\n  ],\r\n  \"formConfig\": {\r\n    \"name\": \"vForm47425\",\r\n    \"modelName\": \"formData\",\r\n    \"refName\": \"vForm\",\r\n    \"rulesName\": \"rules\",\r\n    \"labelWidth\": 80,\r\n    \"labelPosition\": \"left\",\r\n    \"size\": \"\",\r\n    \"labelAlign\": \"label-left-align\",\r\n    \"cssCode\": \"\",\r\n    \"customClass\": \"\",\r\n    \"functions\": \"\",\r\n    \"layoutType\": \"PC\",\r\n    \"jsonVersion\": 3,\r\n    \"disabled\": false,\r\n    \"readonly\": false,\r\n    \"entityName\": \"\",\r\n    \"dicNoList\": [],\r\n    \"onFormCreated\": \"\",\r\n    \"onFormMounted\": \"\",\r\n    \"onFormDataChange\": \"\",\r\n    \"labelFontFamily\": null,\r\n    \"labelFontSize\": null\r\n  }\r\n}","FORMDATA":{}}}|| || 00-43b717a279d4d146ee112bbc7f1e1c01-20ce33357dd4f851-00 ||end
2024-08-05 09:55:15.7330||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-f578228b1d1c9596b39b213171377b8c-0e0e99f4de0bdd7d-00 ||end
2024-08-05 09:55:15.7330||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_FormCollectionController.GetFormOption (ZT.WebApi)|| || 00-f578228b1d1c9596b39b213171377b8c-0e0e99f4de0bdd7d-00 ||end
2024-08-05 09:55:15.7330||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"formId":10241}|| || 00-f578228b1d1c9596b39b213171377b8c-0e0e99f4de0bdd7d-00 ||end
2024-08-05 09:55:15.7330||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":null,"message":null,"data":{"FORMID":10241,"FORMOPTIONS":"{\r\n  \"widgetList\": [\r\n    {\r\n      \"key\": 89673,\r\n      \"type\": \"button\",\r\n      \"icon\": \"svg-icon:button\",\r\n      \"formItemFlag\": false,\r\n      \"options\": {\r\n        \"name\": \"btnBindForm\",\r\n        \"label\": \"common.bindForm\",\r\n        \"labelAlign\": \"\",\r\n        \"columnWidth\": null,\r\n        \"size\": \"small\",\r\n        \"displayStyle\": \"inline-flex\",\r\n        \"disabled\": false,\r\n        \"hidden\": false,\r\n        \"type\": \"warning\",\r\n        \"text\": true,\r\n        \"plain\": false,\r\n        \"round\": false,\r\n        \"circle\": false,\r\n        \"customClass\": [],\r\n        \"labelFontFamily\": \"\",\r\n        \"labelFontSize\": \"\",\r\n        \"labelBold\": false,\r\n        \"labelItalic\": false,\r\n        \"labelUnderline\": false,\r\n        \"labelLineThrough\": false,\r\n        \"labelIconClass\": \"Link\",\r\n        \"labelIconPosition\": \"front\",\r\n        \"labelTooltip\": null,\r\n        \"labelIconType\": \"pl\",\r\n        \"onCreated\": \"\",\r\n        \"onMounted\": \"const parentRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nthis.setHidden(!parentRef.checkButtonPermiss('BindForm'));\",\r\n        \"onClick\": \"const parentRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nconst rows = (parentRef.getWidgetRef(parentRef.tableInfo.tableName)).getSelectionRows();\\r\\nif (rows.length !== 1) {\\r\\n  return parentRef.useMessage.error(this.$t('common.selectNoData', [this.$t('common.bindForm')]));\\r\\n}\\r\\nconst data = {\\r\\n  OBJECTTOCHANGE: rows[0].OBJECTTOCHANGE,\\r\\n  BINDNAME: rows[0].OBJECTTOCHANGE.name,\\r\\n  BINDREVISION: rows[0].OBJECTTOCHANGE.revision,\\r\\n  BINDTABLEID: rows[0][parentRef.tableInfo.tableKey],\\r\\n  BINDTABLE: parentRef.tableInfo.tableName\\r\\n};\\r\\n(this.getWidgetRef('dialogBindForm')).open();\\r\\n(this.getWidgetRef('dialogBindForm')).setValue(data);\"\r\n      },\r\n      \"id\": \"button88910\"\r\n    },\r\n    {\r\n      \"key\": 12910,\r\n      \"type\": \"dialog\",\r\n      \"category\": \"container\",\r\n      \"icon\": \"svg-icon:dialog\",\r\n      \"widgetList\": [\r\n        {\r\n          \"type\": \"dialog-header\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"static-text\",\r\n              \"icon\": \"svg-icon:static-text\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"statictext114746\",\r\n                \"label\": \"static-text\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 100,\r\n                \"hidden\": false,\r\n                \"textContent\": \"绑定表单\",\r\n                \"displayStyle\": \"block\",\r\n                \"whiteSpace\": \"normal\",\r\n                \"columnFiltering\": false,\r\n                \"columnSorting\": false,\r\n                \"columnSortingType\": \"\",\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"MicrosoftYahei\",\r\n                \"labelFontSize\": \"!text-18.7px\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"InfoFilled\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"const rootRef = this.getFormRef().parentFormRef.parentFormRef.parentFormRef;\\r\\nthis.field.options.textContent = this.$t(rootRef.tableInfo.columnCNName) + '--' + this.$t('common.bindForm');\"\r\n              },\r\n              \"id\": \"statictext114746\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogheader52597\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": []\r\n            },\r\n            \"label\": \"dialog-header\"\r\n          },\r\n          \"id\": \"dialogheader52597\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-footer\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button87968\",\r\n                \"label\": \"common.cancel\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"info\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialogBindForm')?.handleClose();\"\r\n              },\r\n              \"id\": \"button87968\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button92812\",\r\n                \"label\": \"common.bind\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"primary\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialogBindForm')?.submit();\"\r\n              },\r\n              \"id\": \"button92812\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogfooter66673\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": [\r\n                \"flex\",\r\n                \"justify-end\"\r\n              ]\r\n            },\r\n            \"label\": \"dialog-footer\"\r\n          },\r\n          \"id\": \"dialogfooter66673\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-body\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"key\": 33730,\r\n              \"type\": \"grid\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"ep:grid\",\r\n              \"cols\": [\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 101753,\r\n                      \"type\": \"input\",\r\n                      \"icon\": \"iconoir:input-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"BINDNAME\",\r\n                        \"label\": \"TMESBaseObject.InstanceName\",\r\n                        \"labelAlign\": \"\",\r\n                        \"type\": \"text\",\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": true,\r\n                        \"hidden\": false,\r\n                        \"clearable\": true,\r\n                        \"showPassword\": false,\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"validation\": [],\r\n                        \"validationHint\": \"\",\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"minLength\": 0,\r\n                        \"maxLength\": 100,\r\n                        \"showWordLimit\": false,\r\n                        \"prefixIcon\": \"\",\r\n                        \"suffixIcon\": \"\",\r\n                        \"appendButton\": false,\r\n                        \"appendButtonDisabled\": false,\r\n                        \"buttonIcon\": \"\",\r\n                        \"iconType\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onInput\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"input30616\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol78355\",\r\n                    \"hidden\": false,\r\n                    \"span\": 12,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol78355\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 101753,\r\n                      \"type\": \"input\",\r\n                      \"icon\": \"iconoir:input-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"BINDREVISION\",\r\n                        \"label\": \"TMESBaseObject.InstanceRev\",\r\n                        \"labelAlign\": \"\",\r\n                        \"type\": \"text\",\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": true,\r\n                        \"hidden\": false,\r\n                        \"clearable\": true,\r\n                        \"showPassword\": false,\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"validation\": [],\r\n                        \"validationHint\": \"\",\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"minLength\": 0,\r\n                        \"maxLength\": 100,\r\n                        \"showWordLimit\": false,\r\n                        \"prefixIcon\": \"\",\r\n                        \"suffixIcon\": \"\",\r\n                        \"appendButton\": false,\r\n                        \"appendButtonDisabled\": false,\r\n                        \"buttonIcon\": \"\",\r\n                        \"iconType\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"this.setHidden(!!!this.getValue());\\r\\n\",\r\n                        \"onInput\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"input71674\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol91515\",\r\n                    \"hidden\": false,\r\n                    \"span\": 12,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol91515\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"grid56145\",\r\n                \"hidden\": false,\r\n                \"gutter\": 12,\r\n                \"colHeight\": null,\r\n                \"customClass\": []\r\n              },\r\n              \"id\": \"grid56145\"\r\n            },\r\n            {\r\n              \"key\": 79014,\r\n              \"type\": \"grid\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"ep:grid\",\r\n              \"cols\": [\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"button\",\r\n                      \"icon\": \"svg-icon:button\",\r\n                      \"formItemFlag\": false,\r\n                      \"options\": {\r\n                        \"name\": \"button33701\",\r\n                        \"label\": \"common.add\",\r\n                        \"labelAlign\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"displayStyle\": \"inline-flex\",\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"type\": \"success\",\r\n                        \"text\": false,\r\n                        \"plain\": false,\r\n                        \"round\": false,\r\n                        \"circle\": false,\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"onCreated\": \"this.detailName=''\",\r\n                        \"onMounted\": \"\",\r\n                        \"onClick\": \"//生成一行默认值\\n(this.getWidgetRef('dtFormbind')).addToTableData();\"\r\n                      },\r\n                      \"id\": \"button33701\"\r\n                    },\r\n                    {\r\n                      \"type\": \"button\",\r\n                      \"icon\": \"svg-icon:button\",\r\n                      \"formItemFlag\": false,\r\n                      \"options\": {\r\n                        \"name\": \"button22579\",\r\n                        \"label\": \"common.delete\",\r\n                        \"labelAlign\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"displayStyle\": \"inline-flex\",\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"type\": \"danger\",\r\n                        \"text\": false,\r\n                        \"plain\": false,\r\n                        \"round\": false,\r\n                        \"circle\": false,\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"onCreated\": \"this.detailName=''\",\r\n                        \"onMounted\": \"\",\r\n                        \"onClick\": \"const rows = (this.getWidgetRef('dtFormbind')).getSelectionRows();\\nif (!rows || rows.length < 1) {\\n  return useMessage.error(this.$t('common.selectNoData', [this.$t('common.delete')]));\\n}\\nrows.forEach(row => {\\n  (this.getWidgetRef('dtFormbind')).deleteTableData(row);\\n})\"\r\n                      },\r\n                      \"id\": \"button22579\"\r\n                    },\r\n                    {\r\n                      \"type\": \"button\",\r\n                      \"icon\": \"svg-icon:button\",\r\n                      \"formItemFlag\": false,\r\n                      \"options\": {\r\n                        \"name\": \"button100093\",\r\n                        \"label\": \"common.refresh\",\r\n                        \"labelAlign\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"displayStyle\": \"inline-flex\",\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"type\": \"primary\",\r\n                        \"text\": false,\r\n                        \"plain\": true,\r\n                        \"round\": false,\r\n                        \"circle\": false,\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"onCreated\": \"this.detailName=''\",\r\n                        \"onMounted\": \"\",\r\n                        \"onClick\": \"//表格的重新加载\\n(this.getWidgetRef('dtFormbind')).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/true)\\n\"\r\n                      },\r\n                      \"id\": \"button100093\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol37984\",\r\n                    \"hidden\": false,\r\n                    \"span\": 24,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": [\r\n                      \"flex\",\r\n                      \"justify-end\"\r\n                    ]\r\n                  },\r\n                  \"id\": \"gridcol37984\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"grid45682\",\r\n                \"hidden\": false,\r\n                \"gutter\": 12,\r\n                \"colHeight\": null,\r\n                \"customClass\": []\r\n              },\r\n              \"id\": \"grid45682\"\r\n            },\r\n            {\r\n              \"key\": 89673,\r\n              \"type\": \"data-table\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"svg-icon:data-table\",\r\n              \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n              \"widgetList\": [\r\n                {\r\n                  \"type\": \"select\",\r\n                  \"icon\": \"svg-icon:select-field\",\r\n                  \"formItemFlag\": true,\r\n                  \"options\": {\r\n                    \"name\": \"FORMID\",\r\n                    \"label\": \"FormDesignOptionsBind.FORMID\",\r\n                    \"labelAlign\": \"\",\r\n                    \"defaultValue\": \"\",\r\n                    \"displayStyle\": \"block\",\r\n                    \"placeholder\": \"\",\r\n                    \"columnWidth\": 120,\r\n                    \"size\": \"\",\r\n                    \"labelWidth\": null,\r\n                    \"labelHidden\": false,\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"clearable\": true,\r\n                    \"filterable\": true,\r\n                    \"allowCreate\": false,\r\n                    \"remote\": false,\r\n                    \"multiple\": false,\r\n                    \"multipleLimit\": 0,\r\n                    \"lazy\": false,\r\n                    \"optionItems\": [\r\n                      {\r\n                        \"label\": \"select 1\",\r\n                        \"value\": \"1\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 2\",\r\n                        \"value\": \"2\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 3\",\r\n                        \"value\": \"3\"\r\n                      }\r\n                    ],\r\n                    \"required\": true,\r\n                    \"requiredHint\": \"\",\r\n                    \"dataSource\": \"FORMDESIGNOPTIONS\",\r\n                    \"optionTagName\": \"key\",\r\n                    \"optionValueName\": \"value\",\r\n                    \"trigger\": \"FORM\",\r\n                    \"columnFiltering\": false,\r\n                    \"columnSorting\": false,\r\n                    \"columnSortingType\": \"\",\r\n                    \"customClass\": [],\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\",\r\n                    \"onRemoteQuery\": \"\",\r\n                    \"onChange\": \"\",\r\n                    \"onFocus\": \"\",\r\n                    \"onBlur\": \"\",\r\n                    \"onValidate\": \"\"\r\n                  },\r\n                  \"id\": \"select87457\"\r\n                },\r\n                {\r\n                  \"type\": \"select\",\r\n                  \"icon\": \"svg-icon:select-field\",\r\n                  \"formItemFlag\": true,\r\n                  \"options\": {\r\n                    \"name\": \"FORMATTABLE\",\r\n                    \"label\": \"FormDesignOptionsBind.FORMATTABLE\",\r\n                    \"labelAlign\": \"\",\r\n                    \"defaultValue\": \"\",\r\n                    \"displayStyle\": \"block\",\r\n                    \"placeholder\": \"\",\r\n                    \"columnWidth\": 120,\r\n                    \"size\": \"\",\r\n                    \"labelWidth\": null,\r\n                    \"labelHidden\": false,\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"clearable\": true,\r\n                    \"filterable\": true,\r\n                    \"allowCreate\": false,\r\n                    \"remote\": false,\r\n                    \"multiple\": false,\r\n                    \"multipleLimit\": 0,\r\n                    \"lazy\": false,\r\n                    \"optionItems\": [\r\n                      {\r\n                        \"label\": \"select 1\",\r\n                        \"value\": \"1\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 2\",\r\n                        \"value\": \"2\"\r\n                      },\r\n                      {\r\n                        \"label\": \"select 3\",\r\n                        \"value\": \"3\"\r\n                      }\r\n                    ],\r\n                    \"required\": false,\r\n                    \"requiredHint\": \"\",\r\n                    \"dataSource\": \"TABLES\",\r\n                    \"optionTagName\": \"key\",\r\n                    \"optionValueName\": \"value\",\r\n                    \"trigger\": null,\r\n                    \"columnFiltering\": false,\r\n                    \"columnSorting\": false,\r\n                    \"columnSortingType\": \"\",\r\n                    \"customClass\": [],\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\",\r\n                    \"onRemoteQuery\": \"\",\r\n                    \"onChange\": \"\",\r\n                    \"onFocus\": \"\",\r\n                    \"onBlur\": \"\",\r\n                    \"onValidate\": \"\"\r\n                  },\r\n                  \"id\": \"select69724\"\r\n                },\r\n                {\r\n                  \"key\": 45001,\r\n                  \"type\": \"button\",\r\n                  \"icon\": \"svg-icon:button\",\r\n                  \"formItemFlag\": false,\r\n                  \"options\": {\r\n                    \"name\": \"button47294\",\r\n                    \"label\": \"FormDesign.Preview\",\r\n                    \"labelAlign\": \"\",\r\n                    \"columnWidth\": 200,\r\n                    \"size\": \"\",\r\n                    \"displayStyle\": \"block\",\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"type\": \"primary\",\r\n                    \"text\": false,\r\n                    \"plain\": true,\r\n                    \"round\": false,\r\n                    \"circle\": false,\r\n                    \"customClass\": [],\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\",\r\n                    \"onClick\": \"const parentForm = this.getFormRef().parentFormRef;\\r\\nconst row = (this.getWidgetRef('dtFormbind')).getValue()[this.subFormRowIndex[0]];\\r\\nparentForm.getWidgetRef('slotFormPreview').open(row);\\r\\n\"\r\n                  },\r\n                  \"id\": \"button47294\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"dtFormbind\",\r\n                \"hidden\": false,\r\n                \"rowSpacing\": 8,\r\n                \"height\": 200,\r\n                \"dataSourceUrl\": \"Sys_FormDesignOptions_Bind/getPageData\",\r\n                \"resultPath\": \"data.rows\",\r\n                \"disabled\": false,\r\n                \"readonly\": false,\r\n                \"onlyChangedData\": true,\r\n                \"loadTreeData\": false,\r\n                \"rowKey\": \"__row_key\",\r\n                \"childrenKey\": \"children\",\r\n                \"stripe\": true,\r\n                \"showIndex\": false,\r\n                \"showCheckBox\": true,\r\n                \"paging\": true,\r\n                \"smallPagination\": false,\r\n                \"border\": true,\r\n                \"size\": \"\",\r\n                \"pagination\": {\r\n                  \"currentPage\": 1,\r\n                  \"pageSizes\": [\r\n                    10,\r\n                    15,\r\n                    20,\r\n                    30,\r\n                    50,\r\n                    100,\r\n                    200\r\n                  ],\r\n                  \"pageSize\": 20,\r\n                  \"total\": 366,\r\n                  \"pagerCount\": 5\r\n                },\r\n                \"defaultValue\": [\r\n                  {\r\n                    \"__row_key\": \"__row_key_78929\",\r\n                    \"FORMID\": \"\",\r\n                    \"FORMATTABLE\": \"\",\r\n                    \"button47294\": \"\"\r\n                  }\r\n                ],\r\n                \"onLoadBefore\": \"const formData = this.formModel;\\r\\nif (!!formData['BINDTABLEID']) {\\r\\n  param.wheres.push(\\r\\n    ...[\\r\\n      {\\r\\n        name: 'BINDTABLE',\\r\\n        value: formData['BINDTABLE'],\\r\\n        displayType: '='\\r\\n      },\\r\\n      { name: 'BINDTABLEID', value: formData['BINDTABLEID'], displayType: '=' }\\r\\n    ]\\r\\n  )\\r\\n}\",\r\n                \"onPageSizeChange\": \"\",\r\n                \"onCurrentPageChange\": \"\",\r\n                \"onSelectionChange\": \"\",\r\n                \"onHideOperationButton\": \"\",\r\n                \"onDisableOperationButton\": \"\",\r\n                \"onGetOperationButtonLabel\": \"\",\r\n                \"onOperationButtonClick\": \"\",\r\n                \"onHeaderClick\": \"\",\r\n                \"onRowClick\": \"\",\r\n                \"onRowDoubleClick\": \"\",\r\n                \"onCellClick\": \"\",\r\n                \"onCellDoubleClick\": \"\",\r\n                \"onGetRowClassName\": \"\",\r\n                \"onGetSpanMethod\": \"\",\r\n                \"label\": \"data-table\"\r\n              },\r\n              \"id\": \"datatable96594\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogbody72699\",\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": []\r\n            },\r\n            \"label\": \"dialog-body\",\r\n            \"columnHeight\": null\r\n          },\r\n          \"id\": \"dialogbody72699\"\r\n        }\r\n      ],\r\n      \"options\": {\r\n        \"name\": \"dialogBindForm\",\r\n        \"label\": \"冻结\",\r\n        \"center\": false,\r\n        \"showClose\": true,\r\n        \"columnWidth\": 50,\r\n        \"draggable\": true,\r\n        \"top\": \"15vh\",\r\n        \"fullscreen\": true,\r\n        \"onOpenBefore\": \"\",\r\n        \"onOpenAfter\": \"\",\r\n        \"onSubmit\": \"const data = await this.getValue();\\r\\n//判断是否有重复表单\\r\\nlet ifPust = true;\\r\\nlet Formbind = data['dtFormbind']?.map((row) => {\\r\\n  if (row.__optype != 'D') {\\r\\n    return row.FORMID\\r\\n  }\\r\\n}).filter((item) => typeof item !== 'undefined')\\r\\nlet enIdSet = new Set(Formbind)\\r\\nif (\\r\\n  Formbind?.length != enIdSet?.size\\r\\n) {\\r\\n  ifPust = false;\\r\\n\\r\\n}\\r\\nconst rootRef = this.getFormRef().parentFormRef.parentFormRef;\\r\\nif (!ifPust) {\\r\\n  rootRef.useMessage.error(this.$t('common.hasDuplicateData'))\\r\\n  return false\\r\\n} else {\\r\\n  const formData = {\\r\\n    mainData: data,\\r\\n    detailData: data['dtFormbind'],\\r\\n    delKeys: data['dtFormbind'].filter(x => x.__optype === 'D').map(x => x.BINDID)\\r\\n  }\\r\\n  const { http } = rootRef.commonApi();\\r\\n  const res = await http('Sys_FormDesignOptions_Bind/save', 'post', { data: formData });\\r\\n  if (!!res?.status) {\\r\\n    rootRef.useMessage.success(this.$t('common.bindSuccess'));\\r\\n    this.formModel[this.widget.options.name] = undefined;\\r\\n  } else {\\r\\n    return false\\r\\n  }\\r\\n\\r\\n}\",\r\n        \"onCloseBefore\": \"\",\r\n        \"onCloseAfter\": \"const parentRef= this.getFormRef();\\r\\nparentRef.setFormData({'dialogBindForm':undefined});\"\r\n      },\r\n      \"id\": \"dialog55340\"\r\n    },\r\n    {\r\n      \"key\": 95488,\r\n      \"type\": \"slot\",\r\n      \"icon\": \"svg-icon:slot-field\",\r\n      \"category\": \"container\",\r\n      \"options\": {\r\n        \"name\": \"slotFormPreview\",\r\n        \"hidden\": false,\r\n        \"border\": false,\r\n        \"disabled\": false,\r\n        \"onCreated\": \"\",\r\n        \"onMounted\": \"\",\r\n        \"label\": \"slot\"\r\n      },\r\n      \"id\": \"slot23612\"\r\n    }\r\n  ],\r\n  \"formConfig\": {\r\n    \"name\": \"vForm105537\",\r\n    \"modelName\": \"formData\",\r\n    \"refName\": \"vForm\",\r\n    \"rulesName\": \"rules\",\r\n    \"labelWidth\": 80,\r\n    \"labelPosition\": \"left\",\r\n    \"size\": \"\",\r\n    \"labelAlign\": \"label-left-align\",\r\n    \"cssCode\": \"\",\r\n    \"customClass\": [],\r\n    \"functions\": \"\",\r\n    \"layoutType\": \"PC\",\r\n    \"jsonVersion\": 3,\r\n    \"disabled\": false,\r\n    \"readonly\": false,\r\n    \"entityName\": \"\",\r\n    \"dicNoList\": [],\r\n    \"onFormCreated\": \"\",\r\n    \"onFormMounted\": \"\",\r\n    \"onFormDataChange\": \"\",\r\n    \"labelFontFamily\": null,\r\n    \"labelFontSize\": null,\r\n    \"lazyDicNoList\": [],\r\n    \"optionItemsObject\": []\r\n  }\r\n}","FORMDATA":{}}}|| || 00-f578228b1d1c9596b39b213171377b8c-0e0e99f4de0bdd7d-00 ||end
2024-08-05 09:55:16.5659||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-0229267c757ac2ae4f38246285624c3b-0a3b8ef352acda04-00 ||end
2024-08-05 09:55:16.5659||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_DictionaryController.GetVueDictionary (ZT.WebApi)|| || 00-0229267c757ac2ae4f38246285624c3b-0a3b8ef352acda04-00 ||end
2024-08-05 09:55:16.5659||Debug||ZT.Core.Filters.ResultFilter||RequestData: [{"DicNo":"SYS_TABLEINFO_TABLEID"},{"DicNo":"MODELINGAUDITTRAIL_EXECUTEACTION"},{"DicNo":"USER_USERTRUENAME"}]|| || 00-0229267c757ac2ae4f38246285624c3b-0a3b8ef352acda04-00 ||end
2024-08-05 09:55:16.5659||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":null,"data":[{"total":24,"data":[{"KEY":25,"VALUE":"11"},{"KEY":24,"VALUE":"2"},{"KEY":6,"VALUE":"fdsa"},{"KEY":7,"VALUE":"ffff"},{"KEY":8,"VALUE":"ffff"},{"KEY":5,"VALUE":"ffff"},{"KEY":9,"VALUE":"ffff31"},{"KEY":10,"VALUE":"ffff32"},{"KEY":11,"VALUE":"ffff33"},{"KEY":12,"VALUE":"ffff34"},{"KEY":13,"VALUE":"ffff41"},{"KEY":3,"VALUE":"gg"},{"KEY":4,"VALUE":"ggdsaf"},{"KEY":17,"VALUE":"HN普通员工"},{"KEY":18,"VALUE":"HN组长"},{"KEY":21,"VALUE":"M4员工"},{"KEY":22,"VALUE":"M5员工"},{"KEY":1,"VALUE":"超级管理员"},{"KEY":14,"VALUE":"广泛大使馆"},{"KEY":16,"VALUE":"蒋永福"},{"KEY":19,"VALUE":"前道普通"},{"KEY":20,"VALUE":"前道组长"},{"KEY":2,"VALUE":"魏冬冬"},{"KEY":15,"VALUE":"夏天钊"}],"config":null,"dicNo":"USER_USERTRUENAME"},{"total":38,"data":[{"KEY":555,"VALUE":"Factory"},{"KEY":543,"VALUE":"FilterTag"},{"KEY":382,"VALUE":"FormCollectionObject"},{"KEY":442,"VALUE":"FormCollectionObject_Format"},{"KEY":361,"VALUE":"FormDesign_Template"},{"KEY":383,"VALUE":"FormDesignOptions"},{"KEY":422,"VALUE":"FormDesignOptions_Bind"},{"KEY":201,"VALUE":"GH_030_EXCEPTIONDISPOSE"},{"KEY":462,"VALUE":"Historymainline"},{"KEY":321,"VALUE":"ManageSysDoc"},{"KEY":548,"VALUE":"ModelingAuditTrail"},{"KEY":1,"VALUE":"Sys_columnAdjustment"},{"KEY":3,"VALUE":"Sys_Dictionary"},{"KEY":4,"VALUE":"Sys_DictionaryList"},{"KEY":261,"VALUE":"Sys_Lang"},{"KEY":5,"VALUE":"Sys_Log"},{"KEY":324,"VALUE":"Sys_Menu_Path"},{"KEY":141,"VALUE":"Sys_Notification"},{"KEY":2,"VALUE":"Sys_Role"},{"KEY":560,"VALUE":"Sys_Role_WorkstationCard"},{"KEY":121,"VALUE":"Sys_Setting"},{"KEY":549,"VALUE":"Sys_TableInfo"},{"KEY":6,"VALUE":"Sys_User"},{"KEY":142,"VALUE":"Sys_User_Notification"},{"KEY":281,"VALUE":"SYS_USER_ROLE"},{"KEY":561,"VALUE":"Sys_User_WorkstationCard"},{"KEY":559,"VALUE":"Sys_WorkstationCard"},{"KEY":461,"VALUE":"SYSLOGINLOG"},{"KEY":544,"VALUE":"Test_Tables"},{"KEY":545,"VALUE":"Test_User"},{"KEY":562,"VALUE":"VM_Inventory_Detail"},{"KEY":557,"VALUE":"WMS_Checking_Detail"},{"KEY":558,"VALUE":"WMS_Checking_Header"},{"KEY":553,"VALUE":"WMS_Inventory"},{"KEY":554,"VALUE":"WMS_Loading"},{"KEY":551,"VALUE":"WMS_Picking_Detail"},{"KEY":552,"VALUE":"WMS_Picking_Header"},{"KEY":556,"VALUE":"WorkCenter"}],"config":null,"dicNo":"SYS_TABLEINFO_TABLEID"},{"total":null,"data":[{"PARENTID":null,"KEY":"Delete","VALUE":"Delete"},{"PARENTID":null,"KEY":"Update","VALUE":"Update"},{"PARENTID":null,"KEY":"Insert","VALUE":"Insert"}],"config":null,"dicNo":"MODELINGAUDITTRAIL_EXECUTEACTION"}]}|| || 00-0229267c757ac2ae4f38246285624c3b-0a3b8ef352acda04-00 ||end
2024-08-05 09:55:16.8528||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-b21fe28df559072e5063fdb1866c9ef1-5ad02f1c7a0386e3-00 ||end
2024-08-05 09:55:16.8528||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_DictionaryController.GetVueDictionary (ZT.WebApi)|| || 00-b21fe28df559072e5063fdb1866c9ef1-5ad02f1c7a0386e3-00 ||end
2024-08-05 09:55:16.8528||Debug||ZT.Core.Filters.ResultFilter||RequestData: [{"DicNo":"FORMDESIGNOPTIONS"},{"DicNo":"TABLES"}]|| || 00-b21fe28df559072e5063fdb1866c9ef1-5ad02f1c7a0386e3-00 ||end
2024-08-05 09:55:16.8528||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":null,"data":[{"total":27,"data":[{"KEY":1,"VALUE":"2021开发语言使用调查-vol"},{"KEY":2,"VALUE":"2021双减政策调查-vol"},{"KEY":123,"VALUE":"id:\"\"456\"\"}'"},{"KEY":10100,"VALUE":"Sys_TableInfo"},{"KEY":10140,"VALUE":"Sys_TableInfo"},{"KEY":10141,"VALUE":"Sys_TableInfo"},{"KEY":10160,"VALUE":"Sys_TableInfo"},{"KEY":10182,"VALUE":"Sys_TableInfo"},{"KEY":10186,"VALUE":"Sys_TableInfo"},{"KEY":10240,"VALUE":"Sys_TableInfo"},{"KEY":10241,"VALUE":"Sys_TableInfo"},{"KEY":10244,"VALUE":"Sys_TableInfo"},{"KEY":10541,"VALUE":"Sys_TableInfo"},{"KEY":10542,"VALUE":"Sys_TableInfo"},{"KEY":10543,"VALUE":"Sys_TableInfo"},{"KEY":10544,"VALUE":"Sys_TableInfo"},{"KEY":10545,"VALUE":"Sys_TableInfo"},{"KEY":10546,"VALUE":"Sys_TableInfo"},{"KEY":10547,"VALUE":"Sys_TableInfo"},{"KEY":10548,"VALUE":"Sys_TableInfo"},{"KEY":10549,"VALUE":"Sys_TableInfo"},{"KEY":10550,"VALUE":"Sys_TableInfo"},{"KEY":10551,"VALUE":"Sys_TableInfo"},{"KEY":10552,"VALUE":"Sys_TableInfo"},{"KEY":10553,"VALUE":"Sys_TableInfo"},{"KEY":10554,"VALUE":"Sys_TableInfo"},{"KEY":21,"VALUE":"测试第三方链接及传参"}],"config":null,"dicNo":"FORMDESIGNOPTIONS"}]}|| || 00-b21fe28df559072e5063fdb1866c9ef1-5ad02f1c7a0386e3-00 ||end
2024-08-05 09:56:09.2254||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-8500f53d937c50fc41bb85958a89a6a1-3fd6506230bfffcd-00 ||end
2024-08-05 09:56:09.2254||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (4ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Sys_FormDesignOptions] AS [s]
        WHERE [s].[FORMID] = 10544) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END|| || 00-8500f53d937c50fc41bb85958a89a6a1-3fd6506230bfffcd-00 ||end
2024-08-05 09:56:09.2565||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (21ms) [Parameters=[@p7='?' (DbType = Int32), @p0='?' (Size = -1), @p1='?' (Size = 30), @p2='?' (DbType = Int32), @p3='?' (Size = 30), @p4='?' (DbType = Date), @p5='?' (DbType = Int32), @p6='?' (Size = 500)], CommandType='Text', CommandTimeout='30']
SET NOCOUNT ON;
UPDATE [Sys_FormDesignOptions] SET [FORMOPTIONS] = @p0, [FORMREVISION] = @p1, [FORMSTATUS] = @p2, [MODIFIER] = @p3, [MODIFYDATE] = @p4, [MODIFYID] = @p5, [TITLE] = @p6
WHERE [FORMID] = @p7;
SELECT @@ROWCOUNT;|| || 00-8500f53d937c50fc41bb85958a89a6a1-3fd6506230bfffcd-00 ||end
2024-08-05 09:56:09.2879||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-8500f53d937c50fc41bb85958a89a6a1-3fd6506230bfffcd-00 ||end
2024-08-05 09:56:09.2879||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_FormDesignOptionsController.Update (ZT.WebApi)|| || 00-8500f53d937c50fc41bb85958a89a6a1-3fd6506230bfffcd-00 ||end
2024-08-05 09:56:09.2879||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"mainData":{"FORMID":10544,"TITLE":"Sys_TableInfo","DARAGGEOPTIONS":null,"FORMOPTIONS":{"widgetList":[{"type":"grid","category":"container","icon":"ep:grid","cols":[{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"key":39418,"type":"link","icon":"svg-icon:textLink","formItemFlag":false,"options":{"name":"link27855","label":"库存","labelAlign":"","type":"default","columnWidth":100,"displayStyle":"block","underline":true,"disabled":false,"hidden":false,"target":"_self","href":"","customClass":["linkColorBlack"],"labelFontFamily":"","labelFontSize":"!text-14px","labelBold":true,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Grid","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","columnFiltering":false,"columnSorting":false,"columnSortingType":"","onCreated":"","onMounted":"","onChange":"","onValidate":"","lineThrough":false},"id":"link27855"}],"options":{"name":"gridcol61024","hidden":false,"span":6,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":[]},"id":"gridcol61024"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"key":48560,"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"searchParam","label":"input","labelAlign":"","type":"text","defaultValue":"","displayStyle":"inline-flex","placeholder":"敲击回车查询","columnWidth":200,"size":"","labelWidth":null,"labelHidden":true,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":[],"validationHint":"","columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":["mb-0"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":100,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"if (!this.getFormRef().checkButtonPermiss('Search')) {\r\n  this.setHidden(true);\r\n} else {\r\n  const timer = setInterval(() => {\r\n    if (!!this.getFormRef().tableInfo) {\r\n      clearInterval(timer);\r\n      this.setHidden(!!!this.getFormRef().tableInfo.expressField);\r\n      if (!!this.getFormRef().tableInfo.expressField) {\r\n        this.setValue(this.getFormRef().currentRoute.query?.queryParam ?? '');\r\n      }\r\n    }\r\n  }, 200)\r\n}\r\n\r\n","onInput":"","onChange":"","onFocus":"","onBlur":"this.setValue(this.getValue().trim());","onValidate":"","onKeypressEnter":"this.setValue(this.getValue().trim());\r\n(this.getWidgetRef(this.getFormRef().tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false)\r\n"},"id":"input103233"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnSearch","label":"common.search","labelAlign":"","columnWidth":null,"size":"","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"primary","text":false,"plain":true,"round":false,"circle":false,"customClass":["pr-5px","pt-1px"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Search","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('Search'));","onClick":"//表格的重新加载\n(this.getWidgetRef(this.getFormRef().tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false)\n"},"id":"button46992"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnAdd","label":"common.add","labelAlign":"","columnWidth":null,"size":"","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"success","text":false,"plain":false,"round":false,"circle":false,"customClass":["pr-5px","pt-1px"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"CirclePlus","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('Add'));","onClick":"(this.getWidgetRef('dialogEditForm')).open();\n(this.getWidgetRef('dialogEditForm')).setValue({ __optype: 'A' });\n(this.getWidgetRef('dialogEditForm'))['__currentAction']='ADD';"},"id":"button106344"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnEdit","label":"common.edit","labelAlign":"","columnWidth":null,"size":"","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"primary","text":false,"plain":false,"round":false,"circle":false,"customClass":["pr-5px","pt-1px"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Edit","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('Update'));","onClick":"const rootForm = this.getFormRef();\nconst rows = (this.getWidgetRef(rootForm.tableInfo.tableName)).getSelectionRows();\nif (rows.length !== 1) {\n  return rootForm.useMessage.error(this.$t('common.selectNoData', [this.$t('common.edit')]));\n}\n(this.getWidgetRef('dialogEditForm')).open();\n(this.getWidgetRef('dialogEditForm')).setValue({ ...rows[0], __optype: 'U' });\n(this.getWidgetRef('dialogEditForm'))['__currentAction'] = 'EDIT';"},"id":"button121732"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnDel","label":"common.delete","labelAlign":"","columnWidth":null,"size":"","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"danger","text":false,"plain":false,"round":false,"circle":false,"customClass":["pr-5px","pt-1px"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Delete","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('Delete'));","onClick":"const rootForm = this.getFormRef();\nconst rows = (rootForm.getWidgetRef(rootForm.tableInfo.tableName)).getSelectionRows();\nif (!rows || rows.length < 1) {\n  return rootForm.useMessage.error(this.$t('common.selectNoData', [this.$t('common.delete')]));\n}\nthis.$confirm(this.$t('common.delMessage'), this.$t('common.delWarning'), {\n  confirmButtonText: this.$t('common.delOk'),\n  cancelButtonText: this.$t('common.delCancel'),\n  type: 'warning',\n  center: true\n}).then(() => {\n  let delKeys = rows.map(x => x[rootForm.tableInfo.tableKey]);\n  const { http } = rootForm.commonApi();\n  http(rootForm.getUrl('Del2', true), 'post', { data: delKeys, permissionTable: rootForm.tableInfo.tableName }).then((x) => {\n    if (!!x?.status) {\n      rootForm.useMessage.success(x.message);\n      //表格的重新加载\n      (rootForm.getWidgetRef(rootForm.tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false);\n    }\n  })\n});\n\n\n"},"id":"button20509"},{"key":15003,"type":"dropdown","category":"container","icon":"radix-icons:dropdown-menu","widgetList":[{"type":"dropdown-item","category":"container","internal":true,"draginable":"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$","widgetList":[{"key":69467,"type":"embedded-form","icon":"svg-icon:embedded-form-field","category":"container","widgetList":[],"options":{"name":"embeddedform82044","hidden":false,"border":false,"disabled":false,"formOptionParams":[{"key":"formId","value":10141}],"form":{"customClass":["p-0"]},"onCreated":"","onMounted":"","label":"embedded-form","customClass":[]},"id":"embeddedform82044"}],"options":{"name":"dropdownitem29169","label":"action 1","disabled":false,"hidden":false,"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('Copy'));","onClick":"","columnWidth":null,"customClass":["!px-0"]},"id":"dropdownitem29169"},{"type":"dropdown-item","category":"container","internal":true,"draginable":"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$","widgetList":[{"key":79765,"type":"embedded-form","icon":"svg-icon:embedded-form-field","category":"container","widgetList":[],"options":{"name":"embeddedform68183","hidden":false,"border":false,"disabled":false,"formOptionParams":[{"key":"formId","value":10240}],"form":{"customClass":["p-0"]},"onCreated":"","onMounted":"","label":"embedded-form"},"id":"embeddedform68183"}],"options":{"name":"dropdownitem100911","label":"action 2","disabled":false,"hidden":false,"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('Freeze'));","onClick":"","columnWidth":null,"customClass":["!px-0"]},"id":"dropdownitem100911"},{"type":"dropdown-item","category":"container","internal":true,"draginable":"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$","widgetList":[{"key":96151,"type":"embedded-form","icon":"svg-icon:embedded-form-field","category":"container","widgetList":[{"type":"embedded-form-slot","icon":"embedded-form-slot","category":"container","draginable":"^.+$","internal":true,"widgetList":[],"options":{"name":"slotFormPreview","hidden":false,"border":false,"disabled":false,"designable":false,"onCreated":"","onMounted":"","label":"embedded-form-slot"},"id":"embeddedformslot104952"}],"options":{"name":"embeddedform61663","hidden":false,"border":false,"disabled":false,"formOptionParams":[{"key":"formId","value":10241}],"form":{"customClass":["p-0"]},"onCreated":"","onMounted":"","label":"embedded-form"},"id":"embeddedform61663"}],"options":{"name":"dropdownitem33398","label":"action 3","disabled":false,"hidden":false,"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('BindForm'));","onClick":"","columnWidth":null,"customClass":["!px-0"]},"id":"dropdownitem33398"},{"type":"dropdown-item","category":"container","internal":true,"draginable":"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$","widgetList":[{"key":32789,"type":"embedded-form","icon":"svg-icon:embedded-form-field","category":"container","widgetList":[],"options":{"name":"embeddedform57613","hidden":false,"border":false,"disabled":false,"formOptionParams":[{"key":"formId","value":10244}],"form":{"customClass":["p-0"]},"onCreated":"","onMounted":"","label":"embedded-form"},"id":"embeddedform57613"}],"options":{"name":"dropdownitem22281","label":"action 7","disabled":false,"hidden":false,"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('historicalTracing'));","onClick":"","columnWidth":null,"customClass":["!px-0"]},"id":"dropdownitem22281"},{"type":"dropdown-item","category":"container","internal":true,"draginable":"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$","widgetList":[{"key":97916,"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"button62675","label":"common.exportPage","labelAlign":"","columnWidth":null,"size":"small","displayStyle":"block","disabled":false,"hidden":false,"type":"success","text":true,"plain":false,"round":false,"circle":false,"customClass":[],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Download","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"","onMounted":"","onClick":"const rootForm = this.getFormRef().parentFormRef;\r\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\r\n$table.localExport();"},"id":"button62675"}],"options":{"name":"dropdownitem86037","label":"common.exportPage","disabled":false,"hidden":false,"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('Export'));","onClick":"","columnWidth":null,"customClass":["!px-0"]},"id":"dropdownitem86037"},{"type":"dropdown-item","category":"container","internal":true,"draginable":"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$","widgetList":[{"key":97916,"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"button80999","label":"common.exportAll","labelAlign":"","columnWidth":null,"size":"small","displayStyle":"block","disabled":false,"hidden":false,"type":"success","text":true,"plain":false,"round":false,"circle":false,"customClass":[],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"cloud-download","labelIconPosition":"front","labelTooltip":null,"labelIconType":"sv","onCreated":"","onMounted":"","onClick":"const rootForm = this.getFormRef().parentFormRef;\r\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\r\nconst exportUrl = rootForm.getUrl('export');\r\nconst downloadUrl = rootForm.getUrl('downloadFile', true);\r\n$table.remoteExport({ exportUrl, downloadUrl });"},"id":"button80999"}],"options":{"name":"dropdownitem96091","label":"common.exportAll","disabled":false,"hidden":false,"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('RemoteExport'));","onClick":"","columnWidth":null,"customClass":["!px-0"]},"id":"dropdownitem96091"},{"type":"dropdown-item","category":"container","internal":true,"draginable":"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$","widgetList":[{"key":97916,"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"button58159","label":"common.adjustColumns","labelAlign":"","columnWidth":null,"size":"small","displayStyle":"block","disabled":false,"hidden":false,"type":"primary","text":true,"plain":false,"round":false,"circle":false,"customClass":[],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Setting","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"","onMounted":"","onClick":"const rootForm = this.getFormRef().parentFormRef;\r\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\r\n$table.openColumnDrawer();"},"id":"button58159"}],"options":{"name":"dropdownitem45249","label":"common.adjustColumns","disabled":false,"hidden":false,"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"this.setHidden(!this.getFormRef().checkButtonPermiss('AdjustCol'));","onClick":"","columnWidth":null,"customClass":["!px-0"]},"id":"dropdownitem45249"}],"options":{"name":"dropdown71045","label":"common.more","defaultValue":"","columnWidth":null,"size":"","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"success","text":false,"plain":true,"round":false,"circle":false,"customClass":["pt-1px"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"","onCommand":""},"id":"dropdown71045"}],"options":{"name":"gridcol80749","hidden":false,"span":18,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":["flex","justify-end","itemAlignTop","pt-1px","h-27px"]},"id":"gridcol80749"}],"options":{"name":"gridTitle","hidden":false,"gutter":0,"colHeight":null,"customClass":["py-8px"]},"id":"grid17420"},{"key":27644,"type":"data-table","category":"container","icon":"svg-icon:data-table","draginable":"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$","widgetList":[{"type":"link","icon":"svg-icon:textLink","formItemFlag":false,"options":{"name":"MATERIAL","label":"WMSInventory.MATERIAL","labelAlign":"","type":"primary","columnWidth":110,"displayStyle":"block","underline":true,"disabled":false,"hidden":false,"target":"_self","href":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","columnFixed":"left","columnFiltering":true,"columnSorting":false,"columnSortingType":"","onCreated":"","onMounted":"","onChange":"","onValidate":"","onClick":"(this.getWidgetRef('dialogEditForm')).open();\r\nconst rootForm = this.getFormRef();\r\nconst row = (this.getWidgetRef(rootForm.tableInfo.tableName)).getValue(false)[this.subFormRowIndex];\r\n(this.getWidgetRef('dialogEditForm')).setValue({ ...row, __optype: 'U' });\r\n(this.getWidgetRef('dialogEditForm'))['__currentAction'] = 'EDIT';"},"id":"link24523"},{"type":"textarea","icon":"bi:textarea-resize","formItemFlag":true,"options":{"name":"MATERIAL_DESCRIPTION","label":"WMSInventory.MATERIAL_DESCRIPTION","labelAlign":"","rows":3,"defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":220,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"required":false,"requiredHint":"","validation":"","validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"zhCn","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":100,"showWordLimit":true,"onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onValidate":""},"id":"textarea97487"},{"type":"number","icon":"svg-icon:number-field","formItemFlag":true,"options":{"name":"MENGE","label":"WMSInventory.MENGE","labelAlign":"","defaultValue":null,"displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"required":true,"requiredHint":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","min":-10000000000,"max":100000000000,"precision":2,"step":1,"controlsPosition":"","onCreated":"","onMounted":"","onChange":"","onFocus":"","onBlur":"","onValidate":""},"id":"number39622"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"UNIT","label":"WMSInventory.UNIT","labelAlign":"","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"zhCn","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":6,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input75077"},{"type":"number","icon":"svg-icon:number-field","formItemFlag":true,"options":{"name":"ONLINE_QTY","label":"WMSInventory.ONLINE_QTY","labelAlign":"","defaultValue":null,"displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":true,"disabled":false,"hidden":false,"required":false,"requiredHint":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","min":-10000000000,"max":100000000000,"precision":2,"step":1,"controlsPosition":"","onCreated":"","onMounted":"","onChange":"","onFocus":"","onBlur":"","onValidate":""},"id":"number34132"},{"type":"select","icon":"svg-icon:select-field","formItemFlag":true,"options":{"name":"FILTERTAGS","label":"FilterTag.FilterTag","labelAlign":"","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"filterable":true,"allowCreate":false,"remote":false,"multiple":false,"multipleLimit":0,"lazy":false,"optionItems":[{"label":"select 1","value":"1"},{"label":"select 2","value":"2"},{"label":"select 3","value":"3"}],"required":false,"requiredHint":"","dataSource":"FILTERTAG","optionTagName":"key","optionValueName":"value","trigger":null,"formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"","onRemoteQuery":"","onRemoteQueryBefore":"","onChange":"","onFocus":"","onBlur":"","onValidate":""},"id":"select39495"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"MO_LOT_NUMBER","label":"WMSPickingDetail.MO_LOT_NUMBER","labelAlign":"","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":50,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input59705"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"SO_LOT_NUMBER","label":"WMSPickingDetail.SO_LOT_NUMBER","labelAlign":"","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":220,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":300,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input97805"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"SUPPLIER_NAME","label":"WMSPickingDetail.SUPPLIER_NAME","labelAlign":"","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":120,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":100,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input82624"}],"options":{"name":"WMS_Inventory","hidden":false,"rowSpacing":8,"height":"calc(100vh - var(--tags-view-height) - var(--top-tool-height) - var(--app-footer-height) - 48px - 52px - 1px)","dataSourceUrl":"WMS_Inventory/getPageData","resultPath":"data.rows","disabled":false,"readonly":true,"loadTreeData":false,"rowKey":"__row_key","childrenKey":"children","stripe":true,"showIndex":false,"showCheckBox":true,"paging":true,"smallPagination":false,"border":true,"size":"default","pagination":{"currentPage":1,"pageSizes":[10,15,20,30,50,100,200],"pageSize":20,"total":366,"pagerCount":5},"defaultValue":[{}],"onLoadBefore":"const searchParamVal = (this.getWidgetRef('searchParam'))?.getValue();\r\nif (!!searchParamVal) {\r\n  param.wheres.push({ name: this.getFormRef().tableInfo.expressField, value: searchParamVal, displayType: 'like' });\r\n}","onPageSizeChange":"","onCurrentPageChange":"","onSelectionChange":"","onHideOperationButton":"","onDisableOperationButton":"","onGetOperationButtonLabel":"","onOperationButtonClick":"","onHeaderClick":"","onRowClick":"","onRowDoubleClick":"","onCellClick":"","onCellDoubleClick":"","onGetRowClassName":"","onGetSpanMethod":"","label":"库存","showSummary":true},"id":"datatable68018"},{"type":"dialog","category":"container","icon":"svg-icon:dialog","widgetList":[{"type":"dialog-header","category":"container","internal":true,"draginable":"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$","widgetList":[{"type":"static-text","icon":"svg-icon:static-text","formItemFlag":false,"options":{"name":"statictext63806","label":"static-text","labelAlign":"","columnWidth":100,"hidden":false,"textContent":"编辑","displayStyle":"block","whiteSpace":"normal","columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":[],"labelFontFamily":"MicrosoftYahei","labelFontSize":"!text-18.7px","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"InfoFilled","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"","onMounted":"const parentRef = this.getFormRef().parentFormRef;\r\nconst action = parentRef.getWidgetRef('dialogEditForm')['__currentAction'];\r\nthis.field.options.textContent = this.$t(parentRef.tableInfo.columnCNName) + '--' + this.$t('common.look');"},"id":"statictext63806"}],"options":{"name":"dialogheader46762","columnWidth":80,"columnHeight":35,"hidden":false,"form":{"size":"","labelPosition":"left","labelAlign":"label-left-align","labelWidth":80,"customClass":[]},"label":"dialog-header"},"id":"dialogheader46762"},{"type":"dialog-footer","category":"container","internal":true,"draginable":"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$","widgetList":[{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"button21984","label":"common.cancel","labelAlign":"","columnWidth":75,"size":"","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"info","text":false,"plain":false,"round":false,"circle":false,"customClass":[],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"","onClick":"const parentRef= this.getFormRef().parentFormRef;\r\nparentRef.getWidgetRef('dialogEditForm')?.handleClose();"},"id":"button21984"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnReset","label":"common.reset","labelAlign":"","columnWidth":75,"size":"","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"warning","text":false,"plain":false,"round":false,"circle":false,"customClass":[],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"//获取表单数据\r\nconst parentRef = this.getFormRef().parentFormRef;\r\nsetTimeout(async () => {\r\n  this._formdata = await parentRef.getWidgetRef('dialogEditForm')?.getValue(false);\r\n}, 1000)\r\n\r\n","onClick":"const parentRef= this.getFormRef().parentFormRef;\r\nparentRef.getWidgetRef('dialogEditForm')?.refresh(this._formdata);"},"id":"button84251"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"button34967","label":"common.submit","labelAlign":"","columnWidth":75,"size":"","displayStyle":"inline-flex","disabled":false,"hidden":true,"type":"primary","text":false,"plain":false,"round":false,"circle":false,"customClass":[],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"","onClick":"const parentRef= this.getFormRef().parentFormRef;\r\nparentRef.getWidgetRef('dialogEditForm')?.submit();"},"id":"button34967"}],"options":{"name":"dialogfooter31118","columnWidth":80,"columnHeight":35,"hidden":false,"form":{"size":"small","labelPosition":"left","labelAlign":"label-left-align","labelWidth":80,"customClass":["flex","justify-end"]},"label":"dialog-footer"},"id":"dialogfooter31118"},{"type":"dialog-body","category":"container","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"key":92283,"type":"grid","category":"container","icon":"ep:grid","cols":[{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"MATERIAL","label":"WMSInventory.MATERIAL","labelAlign":"label-right-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":true,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":[],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":50,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input45172"}],"options":{"name":"gridcol89625","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol89625"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"textarea","icon":"bi:textarea-resize","formItemFlag":true,"options":{"name":"MATERIAL_DESCRIPTION","label":"WMSInventory.MATERIAL_DESCRIPTION","labelAlign":"label-right-align","rows":3,"defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"required":false,"requiredHint":"","validation":"","validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":100,"showWordLimit":true,"onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onValidate":""},"id":"textarea27889"}],"options":{"name":"gridcol80661","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol80661"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"select","icon":"svg-icon:select-field","formItemFlag":true,"options":{"name":"FILTERTAGS","label":"FilterTag.FilterTag","labelAlign":"label-right-align","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"filterable":true,"allowCreate":false,"remote":false,"multiple":false,"multipleLimit":0,"lazy":false,"optionItems":[{"label":"select 1","value":"1"},{"label":"select 2","value":"2"},{"label":"select 3","value":"3"}],"required":false,"requiredHint":"","dataSource":"FILTERTAG","optionTagName":"key","optionValueName":"value","trigger":null,"formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","onCreated":"","onMounted":"","onRemoteQuery":"","onRemoteQueryBefore":"","onChange":"","onFocus":"","onBlur":"","onValidate":""},"id":"select103174"}],"options":{"name":"gridcol36277","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol36277"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"number","icon":"svg-icon:number-field","formItemFlag":true,"options":{"name":"MENGE","label":"WMSInventory.MENGE","labelAlign":"label-right-align","defaultValue":null,"displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"required":true,"requiredHint":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","min":-10000000000,"max":100000000000,"precision":2,"step":1,"controlsPosition":"","onCreated":"","onMounted":"","onChange":"","onFocus":"","onBlur":"","onValidate":""},"id":"number86330"}],"options":{"name":"gridcol18428","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol18428"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"UNIT","label":"WMSInventory.UNIT","labelAlign":"label-right-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":6,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input72157"}],"options":{"name":"gridcol71331","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol71331"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"number","icon":"svg-icon:number-field","formItemFlag":true,"options":{"name":"ONLINE_QTY","label":"WMSInventory.ONLINE_QTY","labelAlign":"label-right-align","defaultValue":null,"displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":true,"disabled":false,"hidden":false,"required":false,"requiredHint":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","min":-10000000000,"max":100000000000,"precision":0,"step":1,"controlsPosition":"","onCreated":"","onMounted":"","onChange":"","onFocus":"","onBlur":"","onValidate":""},"id":"number66265"}],"options":{"name":"gridcol67121","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol67121"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"MO_LOT_NUMBER","label":"WMSPickingDetail.MO_LOT_NUMBER","labelAlign":"label-right-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":50,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input109636"}],"options":{"name":"gridcol76212","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol76212"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"SO_LOT_NUMBER","label":"WMSPickingDetail.SO_LOT_NUMBER","labelAlign":"label-right-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":300,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input79753"}],"options":{"name":"gridcol99965","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol99965"},{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"SUPPLIER_NAME","label":"WMSPickingDetail.SUPPLIER_NAME","labelAlign":"label-right-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":100,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":100,"showWordLimit":true,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input66787"}],"options":{"name":"gridcol4765","hidden":false,"span":8,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":"","onCreated":"","onMounted":""},"id":"gridcol4765"}],"options":{"name":"gridForm","hidden":false,"gutter":12,"colHeight":null},"id":"grid100462"},{"type":"tab","category":"container","icon":"svg-icon:tab","tabs":[{"type":"tab-pane","category":"container","icon":"tab-pane","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"key":79014,"type":"grid","category":"container","icon":"ep:grid","cols":[{"type":"grid-col","category":"container","icon":"grid-col","internal":true,"draginable":"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$","widgetList":[{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnAddDetail_VM_INVENTORY_DETAILs","label":"common.add","labelAlign":"","columnWidth":null,"size":"small","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"success","text":false,"plain":false,"round":false,"circle":false,"customClass":["pl-2"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Plus","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"this.detailName='VM_INVENTORY_DETAILs'","onMounted":"","onClick":"//生成一行默认值\n(this.getWidgetRef(this.detailName)).addToTableData();"},"id":"button36860"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnDelDetail_VM_INVENTORY_DETAILs","label":"common.delete","labelAlign":"","columnWidth":null,"size":"small","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"danger","text":false,"plain":false,"round":false,"circle":false,"customClass":["pl-2"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Minus","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"this.detailName='VM_INVENTORY_DETAILs'","onMounted":"","onClick":"const rows = (this.getWidgetRef(this.detailName)).getSelectionRows();\nif (!rows || rows.length < 1) {\n  return useMessage.error(this.$t('common.selectNoData', [this.$t('common.delete')]));\n}\nrows.forEach(row => {\n  (this.getWidgetRef(this.detailName)).deleteTableData(row);\n})"},"id":"button42081"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnRefreshDetail_VM_INVENTORY_DETAILs","label":"common.refresh","labelAlign":"","columnWidth":null,"size":"small","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"primary","text":false,"plain":true,"round":false,"circle":false,"customClass":["pl-2"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"Refresh","labelIconPosition":"front","labelTooltip":null,"labelIconType":"pl","onCreated":"this.detailName='VM_INVENTORY_DETAILs'","onMounted":"","onClick":"//表格的重新加载\n(this.getWidgetRef(this.detailName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/true)\n"},"id":"button119515"},{"type":"button","icon":"svg-icon:button","formItemFlag":false,"options":{"name":"btnAdjustDetail_VM_INVENTORY_DETAILs","label":"common.adjustColumns","labelAlign":"","columnWidth":null,"size":"small","displayStyle":"inline-flex","disabled":false,"hidden":false,"type":"warning","text":false,"plain":true,"round":false,"circle":false,"customClass":["pl-2"],"labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":"el-icon-set-up","labelIconPosition":"front","labelTooltip":null,"labelIconType":"el","onCreated":"this.detailName='VM_INVENTORY_DETAILs'","onMounted":"","onClick":"//表格的列调整\nconst $table = this.getWidgetRef(this.detailName);\n$table.openColumnDrawer();\n"},"id":"button106086"}],"options":{"name":"gridcol112520","hidden":true,"span":24,"offset":0,"push":0,"pull":0,"responsive":false,"md":12,"sm":12,"xs":12,"customClass":["flex","justify-end"]},"id":"gridcol106232"}],"options":{"name":"grid115896","hidden":false,"gutter":12,"colHeight":null,"customClass":["mb-2"]},"id":"grid54404"},{"key":27644,"type":"data-table","category":"container","icon":"svg-icon:data-table","draginable":"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$","widgetList":[{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"系统","label":"系统","labelAlign":"label-center-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":true,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":6,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input31184"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"动作","label":"动作","labelAlign":"label-center-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":true,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":4,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input81645"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"排序","label":"ColumnAdjust.Order","labelAlign":"label-center-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":8,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input28963"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"日期","label":"日期","labelAlign":"label-center-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":8,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input48859"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"单号","label":"WMSCheckingHeader.ORDER_NO","labelAlign":"label-center-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":true,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":50,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input40520"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"物料","label":"WMSInventory.MATERIAL","labelAlign":"label-center-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":50,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input55295"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"数量","label":"WMSInventory.MENGE","labelAlign":"label-center-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":9,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input69788"},{"type":"input","icon":"iconoir:input-field","formItemFlag":true,"options":{"name":"工厂","label":"Factory.Name","labelAlign":"label-center-align","type":"text","defaultValue":"","displayStyle":"block","placeholder":"","columnWidth":110,"size":"","labelWidth":null,"labelHidden":false,"readonly":false,"disabled":false,"hidden":false,"clearable":true,"showPassword":false,"required":false,"requiredHint":"","validation":["noBlankStart","noBlankEnd"],"validationHint":"","formatter":"","columnFixed":false,"columnFiltering":false,"columnSorting":false,"columnSortingType":"","customClass":"","labelFontFamily":"","labelFontSize":"","labelBold":false,"labelItalic":false,"labelUnderline":false,"labelLineThrough":false,"labelIconClass":null,"labelIconPosition":"rear","labelTooltip":null,"labelIconType":"","minLength":0,"maxLength":30,"showWordLimit":false,"prefixIcon":"","suffixIcon":"","appendButton":false,"appendButtonDisabled":false,"buttonIcon":"","iconType":"","onCreated":"","onMounted":"","onInput":"","onChange":"","onFocus":"","onBlur":"","onKeypressEnter":"","onValidate":""},"id":"input62628"}],"options":{"name":"VM_INVENTORY_DETAILs","hidden":false,"rowSpacing":8,"height":300,"dataSourceUrl":"VM_Inventory_Detail/getPageData","resultPath":"data.rows","disabled":false,"readonly":true,"loadTreeData":false,"rowKey":"__row_key","childrenKey":"children","stripe":true,"showIndex":true,"showCheckBox":true,"paging":true,"smallPagination":false,"border":true,"size":"default","pagination":{"currentPage":1,"pageSizes":[10,15,20,30,50,100,200],"pageSize":20,"total":366,"pagerCount":5},"defaultValue":[{}],"onLoadBefore":"const formData = this.formModel;\r\nif (formData.__optype === 'A') {\r\n  //不执行后续的查询\r\n  return false\r\n} else if (formData.__optype === 'U') {\r\n  const parentRef = this.getFormRef().parentFormRef;\r\n  this.permissionTable = parentRef.tableInfo.tableName;\r\n  param.wheres.push({ name: this.foreignKey, value: formData[\"MATERIAL\"], displayType: '=' })\r\n  param.orderbys = [{ sort: \"排序\", order: \"asc\" }]\r\n}","onPageSizeChange":"","onCurrentPageChange":"","onSelectionChange":"","onHideOperationButton":"","onDisableOperationButton":"","onGetOperationButtonLabel":"","onOperationButtonClick":"","onHeaderClick":"","onRowClick":"","onRowDoubleClick":"","onCellClick":"","onCellDoubleClick":"","onGetRowClassName":"","onGetSpanMethod":"","label":"data-table","onlyChangedData":true,"onCreated":"this.foreignKey = '物料';"},"id":"datatable109857"}],"options":{"name":"tabpane20341","label":"库存明细","disabled":false,"lazy":false,"closable":false,"hidden":false},"id":"tabpane58712"}],"options":{"name":"tabDetail","hidden":false,"tabType":"border-card","tabPosition":"top","defaultValue":"tabpane_detail"},"id":"tab70737"}],"options":{"name":"dialogbody65181","hidden":false,"form":{"size":"","labelPosition":"left","labelAlign":"label-left-align","labelWidth":"auto","customClass":[]},"label":"dialog-body","columnHeight":null},"id":"dialogbody65181"}],"options":{"name":"dialogEditForm","label":"dialog","center":false,"showClose":true,"columnWidth":50,"draggable":true,"top":"15px","fullscreen":true,"onOpenBefore":"","onOpenAfter":"this.setTimeout(() => {\r\n  console.log(this.getBodyFormRef());\r\n  this.getBodyFormRef()?.setReadonlyMode(true);\r\n}, 1200);","onSubmit":"const data = await this.getValue();\nconst formData = {\n  mainData: data\n}\nconst rootForm = this.getFormRef();\nif (['A', 'U'].includes(data.__optype)) {\n  const { http } = rootForm.commonApi();\n  const res = await http(data.__optype === 'A' ? rootForm.getUrl('add2', true) : data.__optype === 'U' ? rootForm.getUrl('update2', true) : '', 'post', { data: formData });\n  if (!!res?.status) {\n    rootForm.useMessage.success(res.message);\n    //表格的重新加载\n    (this.getWidgetRef(rootForm.tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false);\n  } else {\n    return false;\n  }\n}\n","onCloseBefore":"","onCloseAfter":"const parentRef= this.getFormRef();\r\nparentRef.setFormData({'dialogEditForm':undefined});","destroyOnClose":true},"id":"dialog44798"}],"formConfig":{"name":"vForm102307","modelName":"formData","refName":"vForm","rulesName":"rules","labelWidth":80,"labelPosition":"left","size":"small","labelAlign":"label-left-align","cssCode":".itemAlignTop .field-widget-item {\r\n   vertical-align: top;\r\n}\r\n\r\n.linkColorBlack>.el-link {\r\n   --el-link-text-color: var(--el-text-color-primary);\r\n}\r\n\r\n.mb-0{\r\n  margin-bottom: 0 !important;\r\n}","customClass":["px-15px"],"functions":"","layoutType":"PC","jsonVersion":3,"disabled":false,"readonly":false,"entityName":"WMS_Inventory","dicNoList":[],"onFormCreated":"this.tableInfo={\"tableName\":\"WMS_Inventory\",\"columnCNName\":\"库存\",\"tableKey\":\"ID\",\"expressField\":\"MATERIAL\"};\n      this.getUrl=(action,ignoreSuffix)=>'WMS_Inventory/'+action+(!!!ignoreSuffix?'':'');\n      this.onInit();","onFormMounted":"this.onInited();","onFormDataChange":"","labelFontFamily":null,"labelFontSize":null,"lazyDicNoList":[],"optionItemsObject":[]}},"FORMCONFIG":null,"FORMFIELDS":null,"TABLECONFIG":null,"CREATOR":"超级管理员","CREATEDATE":"2024-07-01 00:00:00","CREATEID":1,"MODIFIER":"超级管理员","MODIFYDATE":"2024-08-05 00:00:00","MODIFYID":1,"FORMSTATUS":"0","FORMREVISION":"WMS_Inventory","OBJECTTOCHANGE":{"name":"Sys_TableInfo","revision":"WMS_Inventory","isRDO":true},"index":9}}|| || 00-8500f53d937c50fc41bb85958a89a6a1-3fd6506230bfffcd-00 ||end
2024-08-05 09:56:09.2879||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"312","message":"保存成功","data":"{\"FORMID\":10544,\"TITLE\":\"Sys_TableInfo\",\"DARAGGEOPTIONS\":null,\"FORMOPTIONS\":\"{\\r\\n  \\\"widgetList\\\": [\\r\\n    {\\r\\n      \\\"type\\\": \\\"grid\\\",\\r\\n      \\\"category\\\": \\\"container\\\",\\r\\n      \\\"icon\\\": \\\"ep:grid\\\",\\r\\n      \\\"cols\\\": [\\r\\n        {\\r\\n          \\\"type\\\": \\\"grid-col\\\",\\r\\n          \\\"category\\\": \\\"container\\\",\\r\\n          \\\"icon\\\": \\\"grid-col\\\",\\r\\n          \\\"internal\\\": true,\\r\\n          \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n          \\\"widgetList\\\": [\\r\\n            {\\r\\n              \\\"key\\\": 39418,\\r\\n              \\\"type\\\": \\\"link\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:textLink\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"link27855\\\",\\r\\n                \\\"label\\\": \\\"库存\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"type\\\": \\\"default\\\",\\r\\n                \\\"columnWidth\\\": 100,\\r\\n                \\\"displayStyle\\\": \\\"block\\\",\\r\\n                \\\"underline\\\": true,\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"target\\\": \\\"_self\\\",\\r\\n                \\\"href\\\": \\\"\\\",\\r\\n                \\\"customClass\\\": [\\r\\n                  \\\"linkColorBlack\\\"\\r\\n                ],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"!text-14px\\\",\\r\\n                \\\"labelBold\\\": true,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": \\\"Grid\\\",\\r\\n                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                \\\"columnFiltering\\\": false,\\r\\n                \\\"columnSorting\\\": false,\\r\\n                \\\"columnSortingType\\\": \\\"\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"\\\",\\r\\n                \\\"onChange\\\": \\\"\\\",\\r\\n                \\\"onValidate\\\": \\\"\\\",\\r\\n                \\\"lineThrough\\\": false\\r\\n              },\\r\\n              \\\"id\\\": \\\"link27855\\\"\\r\\n            }\\r\\n          ],\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"gridcol61024\\\",\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"span\\\": 6,\\r\\n            \\\"offset\\\": 0,\\r\\n            \\\"push\\\": 0,\\r\\n            \\\"pull\\\": 0,\\r\\n            \\\"responsive\\\": false,\\r\\n            \\\"md\\\": 12,\\r\\n            \\\"sm\\\": 12,\\r\\n            \\\"xs\\\": 12,\\r\\n            \\\"customClass\\\": []\\r\\n          },\\r\\n          \\\"id\\\": \\\"gridcol61024\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"grid-col\\\",\\r\\n          \\\"category\\\": \\\"container\\\",\\r\\n          \\\"icon\\\": \\\"grid-col\\\",\\r\\n          \\\"internal\\\": true,\\r\\n          \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n          \\\"widgetList\\\": [\\r\\n            {\\r\\n              \\\"key\\\": 48560,\\r\\n              \\\"type\\\": \\\"input\\\",\\r\\n              \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n              \\\"formItemFlag\\\": true,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"searchParam\\\",\\r\\n                \\\"label\\\": \\\"input\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"type\\\": \\\"text\\\",\\r\\n                \\\"defaultValue\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"placeholder\\\": \\\"敲击回车查询\\\",\\r\\n                \\\"columnWidth\\\": 200,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"labelWidth\\\": null,\\r\\n                \\\"labelHidden\\\": true,\\r\\n                \\\"readonly\\\": false,\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"clearable\\\": true,\\r\\n                \\\"showPassword\\\": false,\\r\\n                \\\"required\\\": false,\\r\\n                \\\"requiredHint\\\": \\\"\\\",\\r\\n                \\\"validation\\\": [],\\r\\n                \\\"validationHint\\\": \\\"\\\",\\r\\n                \\\"columnFiltering\\\": false,\\r\\n                \\\"columnSorting\\\": false,\\r\\n                \\\"columnSortingType\\\": \\\"\\\",\\r\\n                \\\"customClass\\\": [\\r\\n                  \\\"mb-0\\\"\\r\\n                ],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": null,\\r\\n                \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"\\\",\\r\\n                \\\"minLength\\\": 0,\\r\\n                \\\"maxLength\\\": 100,\\r\\n                \\\"showWordLimit\\\": false,\\r\\n                \\\"prefixIcon\\\": \\\"\\\",\\r\\n                \\\"suffixIcon\\\": \\\"\\\",\\r\\n                \\\"appendButton\\\": false,\\r\\n                \\\"appendButtonDisabled\\\": false,\\r\\n                \\\"buttonIcon\\\": \\\"\\\",\\r\\n                \\\"iconType\\\": \\\"\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"if (!this.getFormRef().checkButtonPermiss('Search')) {\\\\r\\\\n  this.setHidden(true);\\\\r\\\\n} else {\\\\r\\\\n  const timer = setInterval(() => {\\\\r\\\\n    if (!!this.getFormRef().tableInfo) {\\\\r\\\\n      clearInterval(timer);\\\\r\\\\n      this.setHidden(!!!this.getFormRef().tableInfo.expressField);\\\\r\\\\n      if (!!this.getFormRef().tableInfo.expressField) {\\\\r\\\\n        this.setValue(this.getFormRef().currentRoute.query?.queryParam ?? '');\\\\r\\\\n      }\\\\r\\\\n    }\\\\r\\\\n  }, 200)\\\\r\\\\n}\\\\r\\\\n\\\\r\\\\n\\\",\\r\\n                \\\"onInput\\\": \\\"\\\",\\r\\n                \\\"onChange\\\": \\\"\\\",\\r\\n                \\\"onFocus\\\": \\\"\\\",\\r\\n                \\\"onBlur\\\": \\\"this.setValue(this.getValue().trim());\\\",\\r\\n                \\\"onValidate\\\": \\\"\\\",\\r\\n                \\\"onKeypressEnter\\\": \\\"this.setValue(this.getValue().trim());\\\\r\\\\n(this.getWidgetRef(this.getFormRef().tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false)\\\\r\\\\n\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"input103233\\\"\\r\\n            },\\r\\n            {\\r\\n              \\\"type\\\": \\\"button\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"btnSearch\\\",\\r\\n                \\\"label\\\": \\\"common.search\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": null,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"type\\\": \\\"primary\\\",\\r\\n                \\\"text\\\": false,\\r\\n                \\\"plain\\\": true,\\r\\n                \\\"round\\\": false,\\r\\n                \\\"circle\\\": false,\\r\\n                \\\"customClass\\\": [\\r\\n                  \\\"pr-5px\\\",\\r\\n                  \\\"pt-1px\\\"\\r\\n                ],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": \\\"Search\\\",\\r\\n                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('Search'));\\\",\\r\\n                \\\"onClick\\\": \\\"//表格的重新加载\\\\n(this.getWidgetRef(this.getFormRef().tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false)\\\\n\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"button46992\\\"\\r\\n            },\\r\\n            {\\r\\n              \\\"type\\\": \\\"button\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"btnAdd\\\",\\r\\n                \\\"label\\\": \\\"common.add\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": null,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"type\\\": \\\"success\\\",\\r\\n                \\\"text\\\": false,\\r\\n                \\\"plain\\\": false,\\r\\n                \\\"round\\\": false,\\r\\n                \\\"circle\\\": false,\\r\\n                \\\"customClass\\\": [\\r\\n                  \\\"pr-5px\\\",\\r\\n                  \\\"pt-1px\\\"\\r\\n                ],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": \\\"CirclePlus\\\",\\r\\n                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('Add'));\\\",\\r\\n                \\\"onClick\\\": \\\"(this.getWidgetRef('dialogEditForm')).open();\\\\n(this.getWidgetRef('dialogEditForm')).setValue({ __optype: 'A' });\\\\n(this.getWidgetRef('dialogEditForm'))['__currentAction']='ADD';\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"button106344\\\"\\r\\n            },\\r\\n            {\\r\\n              \\\"type\\\": \\\"button\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"btnEdit\\\",\\r\\n                \\\"label\\\": \\\"common.edit\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": null,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"type\\\": \\\"primary\\\",\\r\\n                \\\"text\\\": false,\\r\\n                \\\"plain\\\": false,\\r\\n                \\\"round\\\": false,\\r\\n                \\\"circle\\\": false,\\r\\n                \\\"customClass\\\": [\\r\\n                  \\\"pr-5px\\\",\\r\\n                  \\\"pt-1px\\\"\\r\\n                ],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": \\\"Edit\\\",\\r\\n                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('Update'));\\\",\\r\\n                \\\"onClick\\\": \\\"const rootForm = this.getFormRef();\\\\nconst rows = (this.getWidgetRef(rootForm.tableInfo.tableName)).getSelectionRows();\\\\nif (rows.length !== 1) {\\\\n  return rootForm.useMessage.error(this.$t('common.selectNoData', [this.$t('common.edit')]));\\\\n}\\\\n(this.getWidgetRef('dialogEditForm')).open();\\\\n(this.getWidgetRef('dialogEditForm')).setValue({ ...rows[0], __optype: 'U' });\\\\n(this.getWidgetRef('dialogEditForm'))['__currentAction'] = 'EDIT';\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"button121732\\\"\\r\\n            },\\r\\n            {\\r\\n              \\\"type\\\": \\\"button\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"btnDel\\\",\\r\\n                \\\"label\\\": \\\"common.delete\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": null,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"type\\\": \\\"danger\\\",\\r\\n                \\\"text\\\": false,\\r\\n                \\\"plain\\\": false,\\r\\n                \\\"round\\\": false,\\r\\n                \\\"circle\\\": false,\\r\\n                \\\"customClass\\\": [\\r\\n                  \\\"pr-5px\\\",\\r\\n                  \\\"pt-1px\\\"\\r\\n                ],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": \\\"Delete\\\",\\r\\n                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('Delete'));\\\",\\r\\n                \\\"onClick\\\": \\\"const rootForm = this.getFormRef();\\\\nconst rows = (rootForm.getWidgetRef(rootForm.tableInfo.tableName)).getSelectionRows();\\\\nif (!rows || rows.length < 1) {\\\\n  return rootForm.useMessage.error(this.$t('common.selectNoData', [this.$t('common.delete')]));\\\\n}\\\\nthis.$confirm(this.$t('common.delMessage'), this.$t('common.delWarning'), {\\\\n  confirmButtonText: this.$t('common.delOk'),\\\\n  cancelButtonText: this.$t('common.delCancel'),\\\\n  type: 'warning',\\\\n  center: true\\\\n}).then(() => {\\\\n  let delKeys = rows.map(x => x[rootForm.tableInfo.tableKey]);\\\\n  const { http } = rootForm.commonApi();\\\\n  http(rootForm.getUrl('Del2', true), 'post', { data: delKeys, permissionTable: rootForm.tableInfo.tableName }).then((x) => {\\\\n    if (!!x?.status) {\\\\n      rootForm.useMessage.success(x.message);\\\\n      //表格的重新加载\\\\n      (rootForm.getWidgetRef(rootForm.tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false);\\\\n    }\\\\n  })\\\\n});\\\\n\\\\n\\\\n\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"button20509\\\"\\r\\n            },\\r\\n            {\\r\\n              \\\"key\\\": 15003,\\r\\n              \\\"type\\\": \\\"dropdown\\\",\\r\\n              \\\"category\\\": \\\"container\\\",\\r\\n              \\\"icon\\\": \\\"radix-icons:dropdown-menu\\\",\\r\\n              \\\"widgetList\\\": [\\r\\n                {\\r\\n                  \\\"type\\\": \\\"dropdown-item\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"key\\\": 69467,\\r\\n                      \\\"type\\\": \\\"embedded-form\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:embedded-form-field\\\",\\r\\n                      \\\"category\\\": \\\"container\\\",\\r\\n                      \\\"widgetList\\\": [],\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"embeddedform82044\\\",\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"border\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"formOptionParams\\\": [\\r\\n                          {\\r\\n                            \\\"key\\\": \\\"formId\\\",\\r\\n                            \\\"value\\\": 10141\\r\\n                          }\\r\\n                        ],\\r\\n                        \\\"form\\\": {\\r\\n                          \\\"customClass\\\": [\\r\\n                            \\\"p-0\\\"\\r\\n                          ]\\r\\n                        },\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"label\\\": \\\"embedded-form\\\",\\r\\n                        \\\"customClass\\\": []\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"embeddedform82044\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"dropdownitem29169\\\",\\r\\n                    \\\"label\\\": \\\"action 1\\\",\\r\\n                    \\\"disabled\\\": false,\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                    \\\"labelFontSize\\\": \\\"\\\",\\r\\n                    \\\"labelBold\\\": false,\\r\\n                    \\\"labelItalic\\\": false,\\r\\n                    \\\"labelUnderline\\\": false,\\r\\n                    \\\"labelLineThrough\\\": false,\\r\\n                    \\\"labelIconClass\\\": null,\\r\\n                    \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                    \\\"labelTooltip\\\": null,\\r\\n                    \\\"labelIconType\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('Copy'));\\\",\\r\\n                    \\\"onClick\\\": \\\"\\\",\\r\\n                    \\\"columnWidth\\\": null,\\r\\n                    \\\"customClass\\\": [\\r\\n                      \\\"!px-0\\\"\\r\\n                    ]\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"dropdownitem29169\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"dropdown-item\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"key\\\": 79765,\\r\\n                      \\\"type\\\": \\\"embedded-form\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:embedded-form-field\\\",\\r\\n                      \\\"category\\\": \\\"container\\\",\\r\\n                      \\\"widgetList\\\": [],\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"embeddedform68183\\\",\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"border\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"formOptionParams\\\": [\\r\\n                          {\\r\\n                            \\\"key\\\": \\\"formId\\\",\\r\\n                            \\\"value\\\": 10240\\r\\n                          }\\r\\n                        ],\\r\\n                        \\\"form\\\": {\\r\\n                          \\\"customClass\\\": [\\r\\n                            \\\"p-0\\\"\\r\\n                          ]\\r\\n                        },\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"label\\\": \\\"embedded-form\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"embeddedform68183\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"dropdownitem100911\\\",\\r\\n                    \\\"label\\\": \\\"action 2\\\",\\r\\n                    \\\"disabled\\\": false,\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                    \\\"labelFontSize\\\": \\\"\\\",\\r\\n                    \\\"labelBold\\\": false,\\r\\n                    \\\"labelItalic\\\": false,\\r\\n                    \\\"labelUnderline\\\": false,\\r\\n                    \\\"labelLineThrough\\\": false,\\r\\n                    \\\"labelIconClass\\\": null,\\r\\n                    \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                    \\\"labelTooltip\\\": null,\\r\\n                    \\\"labelIconType\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('Freeze'));\\\",\\r\\n                    \\\"onClick\\\": \\\"\\\",\\r\\n                    \\\"columnWidth\\\": null,\\r\\n                    \\\"customClass\\\": [\\r\\n                      \\\"!px-0\\\"\\r\\n                    ]\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"dropdownitem100911\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"dropdown-item\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"key\\\": 96151,\\r\\n                      \\\"type\\\": \\\"embedded-form\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:embedded-form-field\\\",\\r\\n                      \\\"category\\\": \\\"container\\\",\\r\\n                      \\\"widgetList\\\": [\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"embedded-form-slot\\\",\\r\\n                          \\\"icon\\\": \\\"embedded-form-slot\\\",\\r\\n                          \\\"category\\\": \\\"container\\\",\\r\\n                          \\\"draginable\\\": \\\"^.+$\\\",\\r\\n                          \\\"internal\\\": true,\\r\\n                          \\\"widgetList\\\": [],\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"slotFormPreview\\\",\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"border\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"designable\\\": false,\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"label\\\": \\\"embedded-form-slot\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"embeddedformslot104952\\\"\\r\\n                        }\\r\\n                      ],\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"embeddedform61663\\\",\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"border\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"formOptionParams\\\": [\\r\\n                          {\\r\\n                            \\\"key\\\": \\\"formId\\\",\\r\\n                            \\\"value\\\": 10241\\r\\n                          }\\r\\n                        ],\\r\\n                        \\\"form\\\": {\\r\\n                          \\\"customClass\\\": [\\r\\n                            \\\"p-0\\\"\\r\\n                          ]\\r\\n                        },\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"label\\\": \\\"embedded-form\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"embeddedform61663\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"dropdownitem33398\\\",\\r\\n                    \\\"label\\\": \\\"action 3\\\",\\r\\n                    \\\"disabled\\\": false,\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                    \\\"labelFontSize\\\": \\\"\\\",\\r\\n                    \\\"labelBold\\\": false,\\r\\n                    \\\"labelItalic\\\": false,\\r\\n                    \\\"labelUnderline\\\": false,\\r\\n                    \\\"labelLineThrough\\\": false,\\r\\n                    \\\"labelIconClass\\\": null,\\r\\n                    \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                    \\\"labelTooltip\\\": null,\\r\\n                    \\\"labelIconType\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('BindForm'));\\\",\\r\\n                    \\\"onClick\\\": \\\"\\\",\\r\\n                    \\\"columnWidth\\\": null,\\r\\n                    \\\"customClass\\\": [\\r\\n                      \\\"!px-0\\\"\\r\\n                    ]\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"dropdownitem33398\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"dropdown-item\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"key\\\": 32789,\\r\\n                      \\\"type\\\": \\\"embedded-form\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:embedded-form-field\\\",\\r\\n                      \\\"category\\\": \\\"container\\\",\\r\\n                      \\\"widgetList\\\": [],\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"embeddedform57613\\\",\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"border\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"formOptionParams\\\": [\\r\\n                          {\\r\\n                            \\\"key\\\": \\\"formId\\\",\\r\\n                            \\\"value\\\": 10244\\r\\n                          }\\r\\n                        ],\\r\\n                        \\\"form\\\": {\\r\\n                          \\\"customClass\\\": [\\r\\n                            \\\"p-0\\\"\\r\\n                          ]\\r\\n                        },\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"label\\\": \\\"embedded-form\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"embeddedform57613\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"dropdownitem22281\\\",\\r\\n                    \\\"label\\\": \\\"action 7\\\",\\r\\n                    \\\"disabled\\\": false,\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                    \\\"labelFontSize\\\": \\\"\\\",\\r\\n                    \\\"labelBold\\\": false,\\r\\n                    \\\"labelItalic\\\": false,\\r\\n                    \\\"labelUnderline\\\": false,\\r\\n                    \\\"labelLineThrough\\\": false,\\r\\n                    \\\"labelIconClass\\\": null,\\r\\n                    \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                    \\\"labelTooltip\\\": null,\\r\\n                    \\\"labelIconType\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('historicalTracing'));\\\",\\r\\n                    \\\"onClick\\\": \\\"\\\",\\r\\n                    \\\"columnWidth\\\": null,\\r\\n                    \\\"customClass\\\": [\\r\\n                      \\\"!px-0\\\"\\r\\n                    ]\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"dropdownitem22281\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"dropdown-item\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"key\\\": 97916,\\r\\n                      \\\"type\\\": \\\"button\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n                      \\\"formItemFlag\\\": false,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"button62675\\\",\\r\\n                        \\\"label\\\": \\\"common.exportPage\\\",\\r\\n                        \\\"labelAlign\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": null,\\r\\n                        \\\"size\\\": \\\"small\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"type\\\": \\\"success\\\",\\r\\n                        \\\"text\\\": true,\\r\\n                        \\\"plain\\\": false,\\r\\n                        \\\"round\\\": false,\\r\\n                        \\\"circle\\\": false,\\r\\n                        \\\"customClass\\\": [],\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": \\\"Download\\\",\\r\\n                        \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onClick\\\": \\\"const rootForm = this.getFormRef().parentFormRef;\\\\r\\\\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\\\\r\\\\n$table.localExport();\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"button62675\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"dropdownitem86037\\\",\\r\\n                    \\\"label\\\": \\\"common.exportPage\\\",\\r\\n                    \\\"disabled\\\": false,\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                    \\\"labelFontSize\\\": \\\"\\\",\\r\\n                    \\\"labelBold\\\": false,\\r\\n                    \\\"labelItalic\\\": false,\\r\\n                    \\\"labelUnderline\\\": false,\\r\\n                    \\\"labelLineThrough\\\": false,\\r\\n                    \\\"labelIconClass\\\": null,\\r\\n                    \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                    \\\"labelTooltip\\\": null,\\r\\n                    \\\"labelIconType\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('Export'));\\\",\\r\\n                    \\\"onClick\\\": \\\"\\\",\\r\\n                    \\\"columnWidth\\\": null,\\r\\n                    \\\"customClass\\\": [\\r\\n                      \\\"!px-0\\\"\\r\\n                    ]\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"dropdownitem86037\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"dropdown-item\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"key\\\": 97916,\\r\\n                      \\\"type\\\": \\\"button\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n                      \\\"formItemFlag\\\": false,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"button80999\\\",\\r\\n                        \\\"label\\\": \\\"common.exportAll\\\",\\r\\n                        \\\"labelAlign\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": null,\\r\\n                        \\\"size\\\": \\\"small\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"type\\\": \\\"success\\\",\\r\\n                        \\\"text\\\": true,\\r\\n                        \\\"plain\\\": false,\\r\\n                        \\\"round\\\": false,\\r\\n                        \\\"circle\\\": false,\\r\\n                        \\\"customClass\\\": [],\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": \\\"cloud-download\\\",\\r\\n                        \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"sv\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onClick\\\": \\\"const rootForm = this.getFormRef().parentFormRef;\\\\r\\\\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\\\\r\\\\nconst exportUrl = rootForm.getUrl('export');\\\\r\\\\nconst downloadUrl = rootForm.getUrl('downloadFile', true);\\\\r\\\\n$table.remoteExport({ exportUrl, downloadUrl });\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"button80999\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"dropdownitem96091\\\",\\r\\n                    \\\"label\\\": \\\"common.exportAll\\\",\\r\\n                    \\\"disabled\\\": false,\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                    \\\"labelFontSize\\\": \\\"\\\",\\r\\n                    \\\"labelBold\\\": false,\\r\\n                    \\\"labelItalic\\\": false,\\r\\n                    \\\"labelUnderline\\\": false,\\r\\n                    \\\"labelLineThrough\\\": false,\\r\\n                    \\\"labelIconClass\\\": null,\\r\\n                    \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                    \\\"labelTooltip\\\": null,\\r\\n                    \\\"labelIconType\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('RemoteExport'));\\\",\\r\\n                    \\\"onClick\\\": \\\"\\\",\\r\\n                    \\\"columnWidth\\\": null,\\r\\n                    \\\"customClass\\\": [\\r\\n                      \\\"!px-0\\\"\\r\\n                    ]\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"dropdownitem96091\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"dropdown-item\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"key\\\": 97916,\\r\\n                      \\\"type\\\": \\\"button\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n                      \\\"formItemFlag\\\": false,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"button58159\\\",\\r\\n                        \\\"label\\\": \\\"common.adjustColumns\\\",\\r\\n                        \\\"labelAlign\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": null,\\r\\n                        \\\"size\\\": \\\"small\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"type\\\": \\\"primary\\\",\\r\\n                        \\\"text\\\": true,\\r\\n                        \\\"plain\\\": false,\\r\\n                        \\\"round\\\": false,\\r\\n                        \\\"circle\\\": false,\\r\\n                        \\\"customClass\\\": [],\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": \\\"Setting\\\",\\r\\n                        \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onClick\\\": \\\"const rootForm = this.getFormRef().parentFormRef;\\\\r\\\\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\\\\r\\\\n$table.openColumnDrawer();\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"button58159\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"dropdownitem45249\\\",\\r\\n                    \\\"label\\\": \\\"common.adjustColumns\\\",\\r\\n                    \\\"disabled\\\": false,\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                    \\\"labelFontSize\\\": \\\"\\\",\\r\\n                    \\\"labelBold\\\": false,\\r\\n                    \\\"labelItalic\\\": false,\\r\\n                    \\\"labelUnderline\\\": false,\\r\\n                    \\\"labelLineThrough\\\": false,\\r\\n                    \\\"labelIconClass\\\": null,\\r\\n                    \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                    \\\"labelTooltip\\\": null,\\r\\n                    \\\"labelIconType\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"this.setHidden(!this.getFormRef().checkButtonPermiss('AdjustCol'));\\\",\\r\\n                    \\\"onClick\\\": \\\"\\\",\\r\\n                    \\\"columnWidth\\\": null,\\r\\n                    \\\"customClass\\\": [\\r\\n                      \\\"!px-0\\\"\\r\\n                    ]\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"dropdownitem45249\\\"\\r\\n                }\\r\\n              ],\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"dropdown71045\\\",\\r\\n                \\\"label\\\": \\\"common.more\\\",\\r\\n                \\\"defaultValue\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": null,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"type\\\": \\\"success\\\",\\r\\n                \\\"text\\\": false,\\r\\n                \\\"plain\\\": true,\\r\\n                \\\"round\\\": false,\\r\\n                \\\"circle\\\": false,\\r\\n                \\\"customClass\\\": [\\r\\n                  \\\"pt-1px\\\"\\r\\n                ],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": null,\\r\\n                \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"\\\",\\r\\n                \\\"onCommand\\\": \\\"\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"dropdown71045\\\"\\r\\n            }\\r\\n          ],\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"gridcol80749\\\",\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"span\\\": 18,\\r\\n            \\\"offset\\\": 0,\\r\\n            \\\"push\\\": 0,\\r\\n            \\\"pull\\\": 0,\\r\\n            \\\"responsive\\\": false,\\r\\n            \\\"md\\\": 12,\\r\\n            \\\"sm\\\": 12,\\r\\n            \\\"xs\\\": 12,\\r\\n            \\\"customClass\\\": [\\r\\n              \\\"flex\\\",\\r\\n              \\\"justify-end\\\",\\r\\n              \\\"itemAlignTop\\\",\\r\\n              \\\"pt-1px\\\",\\r\\n              \\\"h-27px\\\"\\r\\n            ]\\r\\n          },\\r\\n          \\\"id\\\": \\\"gridcol80749\\\"\\r\\n        }\\r\\n      ],\\r\\n      \\\"options\\\": {\\r\\n        \\\"name\\\": \\\"gridTitle\\\",\\r\\n        \\\"hidden\\\": false,\\r\\n        \\\"gutter\\\": 0,\\r\\n        \\\"colHeight\\\": null,\\r\\n        \\\"customClass\\\": [\\r\\n          \\\"py-8px\\\"\\r\\n        ]\\r\\n      },\\r\\n      \\\"id\\\": \\\"grid17420\\\"\\r\\n    },\\r\\n    {\\r\\n      \\\"key\\\": 27644,\\r\\n      \\\"type\\\": \\\"data-table\\\",\\r\\n      \\\"category\\\": \\\"container\\\",\\r\\n      \\\"icon\\\": \\\"svg-icon:data-table\\\",\\r\\n      \\\"draginable\\\": \\\"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\\\",\\r\\n      \\\"widgetList\\\": [\\r\\n        {\\r\\n          \\\"type\\\": \\\"link\\\",\\r\\n          \\\"icon\\\": \\\"svg-icon:textLink\\\",\\r\\n          \\\"formItemFlag\\\": false,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"MATERIAL\\\",\\r\\n            \\\"label\\\": \\\"WMSInventory.MATERIAL\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"type\\\": \\\"primary\\\",\\r\\n            \\\"columnWidth\\\": 110,\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"underline\\\": true,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"target\\\": \\\"_self\\\",\\r\\n            \\\"href\\\": \\\"\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": \\\"left\\\",\\r\\n            \\\"columnFiltering\\\": true,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"\\\",\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\",\\r\\n            \\\"onClick\\\": \\\"(this.getWidgetRef('dialogEditForm')).open();\\\\r\\\\nconst rootForm = this.getFormRef();\\\\r\\\\nconst row = (this.getWidgetRef(rootForm.tableInfo.tableName)).getValue(false)[this.subFormRowIndex];\\\\r\\\\n(this.getWidgetRef('dialogEditForm')).setValue({ ...row, __optype: 'U' });\\\\r\\\\n(this.getWidgetRef('dialogEditForm'))['__currentAction'] = 'EDIT';\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"link24523\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"textarea\\\",\\r\\n          \\\"icon\\\": \\\"bi:textarea-resize\\\",\\r\\n          \\\"formItemFlag\\\": true,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"MATERIAL_DESCRIPTION\\\",\\r\\n            \\\"label\\\": \\\"WMSInventory.MATERIAL_DESCRIPTION\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"rows\\\": 3,\\r\\n            \\\"defaultValue\\\": \\\"\\\",\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"placeholder\\\": \\\"\\\",\\r\\n            \\\"columnWidth\\\": 220,\\r\\n            \\\"size\\\": \\\"\\\",\\r\\n            \\\"labelWidth\\\": null,\\r\\n            \\\"labelHidden\\\": false,\\r\\n            \\\"readonly\\\": false,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"required\\\": false,\\r\\n            \\\"requiredHint\\\": \\\"\\\",\\r\\n            \\\"validation\\\": \\\"\\\",\\r\\n            \\\"validationHint\\\": \\\"\\\",\\r\\n            \\\"formatter\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": false,\\r\\n            \\\"columnFiltering\\\": false,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"zhCn\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"minLength\\\": 0,\\r\\n            \\\"maxLength\\\": 100,\\r\\n            \\\"showWordLimit\\\": true,\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onInput\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onFocus\\\": \\\"\\\",\\r\\n            \\\"onBlur\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"textarea97487\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"number\\\",\\r\\n          \\\"icon\\\": \\\"svg-icon:number-field\\\",\\r\\n          \\\"formItemFlag\\\": true,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"MENGE\\\",\\r\\n            \\\"label\\\": \\\"WMSInventory.MENGE\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"defaultValue\\\": null,\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"placeholder\\\": \\\"\\\",\\r\\n            \\\"columnWidth\\\": 110,\\r\\n            \\\"size\\\": \\\"\\\",\\r\\n            \\\"labelWidth\\\": null,\\r\\n            \\\"labelHidden\\\": false,\\r\\n            \\\"readonly\\\": false,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"required\\\": true,\\r\\n            \\\"requiredHint\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": false,\\r\\n            \\\"columnFiltering\\\": false,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"min\\\": -10000000000,\\r\\n            \\\"max\\\": 100000000000,\\r\\n            \\\"precision\\\": 2,\\r\\n            \\\"step\\\": 1,\\r\\n            \\\"controlsPosition\\\": \\\"\\\",\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onFocus\\\": \\\"\\\",\\r\\n            \\\"onBlur\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"number39622\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"input\\\",\\r\\n          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n          \\\"formItemFlag\\\": true,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"UNIT\\\",\\r\\n            \\\"label\\\": \\\"WMSInventory.UNIT\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"type\\\": \\\"text\\\",\\r\\n            \\\"defaultValue\\\": \\\"\\\",\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"placeholder\\\": \\\"\\\",\\r\\n            \\\"columnWidth\\\": 110,\\r\\n            \\\"size\\\": \\\"\\\",\\r\\n            \\\"labelWidth\\\": null,\\r\\n            \\\"labelHidden\\\": false,\\r\\n            \\\"readonly\\\": false,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"clearable\\\": true,\\r\\n            \\\"showPassword\\\": false,\\r\\n            \\\"required\\\": false,\\r\\n            \\\"requiredHint\\\": \\\"\\\",\\r\\n            \\\"validation\\\": [\\r\\n              \\\"noBlankStart\\\",\\r\\n              \\\"noBlankEnd\\\"\\r\\n            ],\\r\\n            \\\"validationHint\\\": \\\"\\\",\\r\\n            \\\"formatter\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": false,\\r\\n            \\\"columnFiltering\\\": false,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"zhCn\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"minLength\\\": 0,\\r\\n            \\\"maxLength\\\": 6,\\r\\n            \\\"showWordLimit\\\": true,\\r\\n            \\\"prefixIcon\\\": \\\"\\\",\\r\\n            \\\"suffixIcon\\\": \\\"\\\",\\r\\n            \\\"appendButton\\\": false,\\r\\n            \\\"appendButtonDisabled\\\": false,\\r\\n            \\\"buttonIcon\\\": \\\"\\\",\\r\\n            \\\"iconType\\\": \\\"\\\",\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onInput\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onFocus\\\": \\\"\\\",\\r\\n            \\\"onBlur\\\": \\\"\\\",\\r\\n            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"input75077\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"number\\\",\\r\\n          \\\"icon\\\": \\\"svg-icon:number-field\\\",\\r\\n          \\\"formItemFlag\\\": true,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"ONLINE_QTY\\\",\\r\\n            \\\"label\\\": \\\"WMSInventory.ONLINE_QTY\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"defaultValue\\\": null,\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"placeholder\\\": \\\"\\\",\\r\\n            \\\"columnWidth\\\": 110,\\r\\n            \\\"size\\\": \\\"\\\",\\r\\n            \\\"labelWidth\\\": null,\\r\\n            \\\"labelHidden\\\": false,\\r\\n            \\\"readonly\\\": true,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"required\\\": false,\\r\\n            \\\"requiredHint\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": false,\\r\\n            \\\"columnFiltering\\\": false,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"min\\\": -10000000000,\\r\\n            \\\"max\\\": 100000000000,\\r\\n            \\\"precision\\\": 2,\\r\\n            \\\"step\\\": 1,\\r\\n            \\\"controlsPosition\\\": \\\"\\\",\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onFocus\\\": \\\"\\\",\\r\\n            \\\"onBlur\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"number34132\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"select\\\",\\r\\n          \\\"icon\\\": \\\"svg-icon:select-field\\\",\\r\\n          \\\"formItemFlag\\\": true,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"FILTERTAGS\\\",\\r\\n            \\\"label\\\": \\\"FilterTag.FilterTag\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"defaultValue\\\": \\\"\\\",\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"placeholder\\\": \\\"\\\",\\r\\n            \\\"columnWidth\\\": 110,\\r\\n            \\\"size\\\": \\\"\\\",\\r\\n            \\\"labelWidth\\\": null,\\r\\n            \\\"labelHidden\\\": false,\\r\\n            \\\"readonly\\\": false,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"clearable\\\": true,\\r\\n            \\\"filterable\\\": true,\\r\\n            \\\"allowCreate\\\": false,\\r\\n            \\\"remote\\\": false,\\r\\n            \\\"multiple\\\": false,\\r\\n            \\\"multipleLimit\\\": 0,\\r\\n            \\\"lazy\\\": false,\\r\\n            \\\"optionItems\\\": [\\r\\n              {\\r\\n                \\\"label\\\": \\\"select 1\\\",\\r\\n                \\\"value\\\": \\\"1\\\"\\r\\n              },\\r\\n              {\\r\\n                \\\"label\\\": \\\"select 2\\\",\\r\\n                \\\"value\\\": \\\"2\\\"\\r\\n              },\\r\\n              {\\r\\n                \\\"label\\\": \\\"select 3\\\",\\r\\n                \\\"value\\\": \\\"3\\\"\\r\\n              }\\r\\n            ],\\r\\n            \\\"required\\\": false,\\r\\n            \\\"requiredHint\\\": \\\"\\\",\\r\\n            \\\"dataSource\\\": \\\"FILTERTAG\\\",\\r\\n            \\\"optionTagName\\\": \\\"key\\\",\\r\\n            \\\"optionValueName\\\": \\\"value\\\",\\r\\n            \\\"trigger\\\": null,\\r\\n            \\\"formatter\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": false,\\r\\n            \\\"columnFiltering\\\": false,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onRemoteQuery\\\": \\\"\\\",\\r\\n            \\\"onRemoteQueryBefore\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onFocus\\\": \\\"\\\",\\r\\n            \\\"onBlur\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"select39495\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"input\\\",\\r\\n          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n          \\\"formItemFlag\\\": true,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"MO_LOT_NUMBER\\\",\\r\\n            \\\"label\\\": \\\"WMSPickingDetail.MO_LOT_NUMBER\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"type\\\": \\\"text\\\",\\r\\n            \\\"defaultValue\\\": \\\"\\\",\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"placeholder\\\": \\\"\\\",\\r\\n            \\\"columnWidth\\\": 110,\\r\\n            \\\"size\\\": \\\"\\\",\\r\\n            \\\"labelWidth\\\": null,\\r\\n            \\\"labelHidden\\\": false,\\r\\n            \\\"readonly\\\": false,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"clearable\\\": true,\\r\\n            \\\"showPassword\\\": false,\\r\\n            \\\"required\\\": false,\\r\\n            \\\"requiredHint\\\": \\\"\\\",\\r\\n            \\\"validation\\\": [\\r\\n              \\\"noBlankStart\\\",\\r\\n              \\\"noBlankEnd\\\"\\r\\n            ],\\r\\n            \\\"validationHint\\\": \\\"\\\",\\r\\n            \\\"formatter\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": false,\\r\\n            \\\"columnFiltering\\\": false,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"minLength\\\": 0,\\r\\n            \\\"maxLength\\\": 50,\\r\\n            \\\"showWordLimit\\\": true,\\r\\n            \\\"prefixIcon\\\": \\\"\\\",\\r\\n            \\\"suffixIcon\\\": \\\"\\\",\\r\\n            \\\"appendButton\\\": false,\\r\\n            \\\"appendButtonDisabled\\\": false,\\r\\n            \\\"buttonIcon\\\": \\\"\\\",\\r\\n            \\\"iconType\\\": \\\"\\\",\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onInput\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onFocus\\\": \\\"\\\",\\r\\n            \\\"onBlur\\\": \\\"\\\",\\r\\n            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"input59705\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"input\\\",\\r\\n          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n          \\\"formItemFlag\\\": true,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"SO_LOT_NUMBER\\\",\\r\\n            \\\"label\\\": \\\"WMSPickingDetail.SO_LOT_NUMBER\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"type\\\": \\\"text\\\",\\r\\n            \\\"defaultValue\\\": \\\"\\\",\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"placeholder\\\": \\\"\\\",\\r\\n            \\\"columnWidth\\\": 220,\\r\\n            \\\"size\\\": \\\"\\\",\\r\\n            \\\"labelWidth\\\": null,\\r\\n            \\\"labelHidden\\\": false,\\r\\n            \\\"readonly\\\": false,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"clearable\\\": true,\\r\\n            \\\"showPassword\\\": false,\\r\\n            \\\"required\\\": false,\\r\\n            \\\"requiredHint\\\": \\\"\\\",\\r\\n            \\\"validation\\\": [\\r\\n              \\\"noBlankStart\\\",\\r\\n              \\\"noBlankEnd\\\"\\r\\n            ],\\r\\n            \\\"validationHint\\\": \\\"\\\",\\r\\n            \\\"formatter\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": false,\\r\\n            \\\"columnFiltering\\\": false,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"minLength\\\": 0,\\r\\n            \\\"maxLength\\\": 300,\\r\\n            \\\"showWordLimit\\\": true,\\r\\n            \\\"prefixIcon\\\": \\\"\\\",\\r\\n            \\\"suffixIcon\\\": \\\"\\\",\\r\\n            \\\"appendButton\\\": false,\\r\\n            \\\"appendButtonDisabled\\\": false,\\r\\n            \\\"buttonIcon\\\": \\\"\\\",\\r\\n            \\\"iconType\\\": \\\"\\\",\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onInput\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onFocus\\\": \\\"\\\",\\r\\n            \\\"onBlur\\\": \\\"\\\",\\r\\n            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"input97805\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"input\\\",\\r\\n          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n          \\\"formItemFlag\\\": true,\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"SUPPLIER_NAME\\\",\\r\\n            \\\"label\\\": \\\"WMSPickingDetail.SUPPLIER_NAME\\\",\\r\\n            \\\"labelAlign\\\": \\\"\\\",\\r\\n            \\\"type\\\": \\\"text\\\",\\r\\n            \\\"defaultValue\\\": \\\"\\\",\\r\\n            \\\"displayStyle\\\": \\\"block\\\",\\r\\n            \\\"placeholder\\\": \\\"\\\",\\r\\n            \\\"columnWidth\\\": 120,\\r\\n            \\\"size\\\": \\\"\\\",\\r\\n            \\\"labelWidth\\\": null,\\r\\n            \\\"labelHidden\\\": false,\\r\\n            \\\"readonly\\\": false,\\r\\n            \\\"disabled\\\": false,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"clearable\\\": true,\\r\\n            \\\"showPassword\\\": false,\\r\\n            \\\"required\\\": false,\\r\\n            \\\"requiredHint\\\": \\\"\\\",\\r\\n            \\\"validation\\\": [\\r\\n              \\\"noBlankStart\\\",\\r\\n              \\\"noBlankEnd\\\"\\r\\n            ],\\r\\n            \\\"validationHint\\\": \\\"\\\",\\r\\n            \\\"formatter\\\": \\\"\\\",\\r\\n            \\\"columnFixed\\\": false,\\r\\n            \\\"columnFiltering\\\": false,\\r\\n            \\\"columnSorting\\\": false,\\r\\n            \\\"columnSortingType\\\": \\\"\\\",\\r\\n            \\\"customClass\\\": \\\"\\\",\\r\\n            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n            \\\"labelFontSize\\\": \\\"\\\",\\r\\n            \\\"labelBold\\\": false,\\r\\n            \\\"labelItalic\\\": false,\\r\\n            \\\"labelUnderline\\\": false,\\r\\n            \\\"labelLineThrough\\\": false,\\r\\n            \\\"labelIconClass\\\": null,\\r\\n            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n            \\\"labelTooltip\\\": null,\\r\\n            \\\"labelIconType\\\": \\\"\\\",\\r\\n            \\\"minLength\\\": 0,\\r\\n            \\\"maxLength\\\": 100,\\r\\n            \\\"showWordLimit\\\": true,\\r\\n            \\\"prefixIcon\\\": \\\"\\\",\\r\\n            \\\"suffixIcon\\\": \\\"\\\",\\r\\n            \\\"appendButton\\\": false,\\r\\n            \\\"appendButtonDisabled\\\": false,\\r\\n            \\\"buttonIcon\\\": \\\"\\\",\\r\\n            \\\"iconType\\\": \\\"\\\",\\r\\n            \\\"onCreated\\\": \\\"\\\",\\r\\n            \\\"onMounted\\\": \\\"\\\",\\r\\n            \\\"onInput\\\": \\\"\\\",\\r\\n            \\\"onChange\\\": \\\"\\\",\\r\\n            \\\"onFocus\\\": \\\"\\\",\\r\\n            \\\"onBlur\\\": \\\"\\\",\\r\\n            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n            \\\"onValidate\\\": \\\"\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"input82624\\\"\\r\\n        }\\r\\n      ],\\r\\n      \\\"options\\\": {\\r\\n        \\\"name\\\": \\\"WMS_Inventory\\\",\\r\\n        \\\"hidden\\\": false,\\r\\n        \\\"rowSpacing\\\": 8,\\r\\n        \\\"height\\\": \\\"calc(100vh - var(--tags-view-height) - var(--top-tool-height) - var(--app-footer-height) - 48px - 52px - 1px)\\\",\\r\\n        \\\"dataSourceUrl\\\": \\\"WMS_Inventory/getPageData\\\",\\r\\n        \\\"resultPath\\\": \\\"data.rows\\\",\\r\\n        \\\"disabled\\\": false,\\r\\n        \\\"readonly\\\": true,\\r\\n        \\\"loadTreeData\\\": false,\\r\\n        \\\"rowKey\\\": \\\"__row_key\\\",\\r\\n        \\\"childrenKey\\\": \\\"children\\\",\\r\\n        \\\"stripe\\\": true,\\r\\n        \\\"showIndex\\\": false,\\r\\n        \\\"showCheckBox\\\": true,\\r\\n        \\\"paging\\\": true,\\r\\n        \\\"smallPagination\\\": false,\\r\\n        \\\"border\\\": true,\\r\\n        \\\"size\\\": \\\"default\\\",\\r\\n        \\\"pagination\\\": {\\r\\n          \\\"currentPage\\\": 1,\\r\\n          \\\"pageSizes\\\": [\\r\\n            10,\\r\\n            15,\\r\\n            20,\\r\\n            30,\\r\\n            50,\\r\\n            100,\\r\\n            200\\r\\n          ],\\r\\n          \\\"pageSize\\\": 20,\\r\\n          \\\"total\\\": 366,\\r\\n          \\\"pagerCount\\\": 5\\r\\n        },\\r\\n        \\\"defaultValue\\\": [\\r\\n          {}\\r\\n        ],\\r\\n        \\\"onLoadBefore\\\": \\\"const searchParamVal = (this.getWidgetRef('searchParam'))?.getValue();\\\\r\\\\nif (!!searchParamVal) {\\\\r\\\\n  param.wheres.push({ name: this.getFormRef().tableInfo.expressField, value: searchParamVal, displayType: 'like' });\\\\r\\\\n}\\\",\\r\\n        \\\"onPageSizeChange\\\": \\\"\\\",\\r\\n        \\\"onCurrentPageChange\\\": \\\"\\\",\\r\\n        \\\"onSelectionChange\\\": \\\"\\\",\\r\\n        \\\"onHideOperationButton\\\": \\\"\\\",\\r\\n        \\\"onDisableOperationButton\\\": \\\"\\\",\\r\\n        \\\"onGetOperationButtonLabel\\\": \\\"\\\",\\r\\n        \\\"onOperationButtonClick\\\": \\\"\\\",\\r\\n        \\\"onHeaderClick\\\": \\\"\\\",\\r\\n        \\\"onRowClick\\\": \\\"\\\",\\r\\n        \\\"onRowDoubleClick\\\": \\\"\\\",\\r\\n        \\\"onCellClick\\\": \\\"\\\",\\r\\n        \\\"onCellDoubleClick\\\": \\\"\\\",\\r\\n        \\\"onGetRowClassName\\\": \\\"\\\",\\r\\n        \\\"onGetSpanMethod\\\": \\\"\\\",\\r\\n        \\\"label\\\": \\\"库存\\\",\\r\\n        \\\"showSummary\\\": true\\r\\n      },\\r\\n      \\\"id\\\": \\\"datatable68018\\\"\\r\\n    },\\r\\n    {\\r\\n      \\\"type\\\": \\\"dialog\\\",\\r\\n      \\\"category\\\": \\\"container\\\",\\r\\n      \\\"icon\\\": \\\"svg-icon:dialog\\\",\\r\\n      \\\"widgetList\\\": [\\r\\n        {\\r\\n          \\\"type\\\": \\\"dialog-header\\\",\\r\\n          \\\"category\\\": \\\"container\\\",\\r\\n          \\\"internal\\\": true,\\r\\n          \\\"draginable\\\": \\\"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\\\",\\r\\n          \\\"widgetList\\\": [\\r\\n            {\\r\\n              \\\"type\\\": \\\"static-text\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:static-text\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"statictext63806\\\",\\r\\n                \\\"label\\\": \\\"static-text\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": 100,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"textContent\\\": \\\"编辑\\\",\\r\\n                \\\"displayStyle\\\": \\\"block\\\",\\r\\n                \\\"whiteSpace\\\": \\\"normal\\\",\\r\\n                \\\"columnFiltering\\\": false,\\r\\n                \\\"columnSorting\\\": false,\\r\\n                \\\"columnSortingType\\\": \\\"\\\",\\r\\n                \\\"customClass\\\": [],\\r\\n                \\\"labelFontFamily\\\": \\\"MicrosoftYahei\\\",\\r\\n                \\\"labelFontSize\\\": \\\"!text-18.7px\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": \\\"InfoFilled\\\",\\r\\n                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"const parentRef = this.getFormRef().parentFormRef;\\\\r\\\\nconst action = parentRef.getWidgetRef('dialogEditForm')['__currentAction'];\\\\r\\\\nthis.field.options.textContent = this.$t(parentRef.tableInfo.columnCNName) + '--' + this.$t('common.look');\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"statictext63806\\\"\\r\\n            }\\r\\n          ],\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"dialogheader46762\\\",\\r\\n            \\\"columnWidth\\\": 80,\\r\\n            \\\"columnHeight\\\": 35,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"form\\\": {\\r\\n              \\\"size\\\": \\\"\\\",\\r\\n              \\\"labelPosition\\\": \\\"left\\\",\\r\\n              \\\"labelAlign\\\": \\\"label-left-align\\\",\\r\\n              \\\"labelWidth\\\": 80,\\r\\n              \\\"customClass\\\": []\\r\\n            },\\r\\n            \\\"label\\\": \\\"dialog-header\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"dialogheader46762\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"dialog-footer\\\",\\r\\n          \\\"category\\\": \\\"container\\\",\\r\\n          \\\"internal\\\": true,\\r\\n          \\\"draginable\\\": \\\"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\\\",\\r\\n          \\\"widgetList\\\": [\\r\\n            {\\r\\n              \\\"type\\\": \\\"button\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"button21984\\\",\\r\\n                \\\"label\\\": \\\"common.cancel\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": 75,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"type\\\": \\\"info\\\",\\r\\n                \\\"text\\\": false,\\r\\n                \\\"plain\\\": false,\\r\\n                \\\"round\\\": false,\\r\\n                \\\"circle\\\": false,\\r\\n                \\\"customClass\\\": [],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": null,\\r\\n                \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"\\\",\\r\\n                \\\"onClick\\\": \\\"const parentRef= this.getFormRef().parentFormRef;\\\\r\\\\nparentRef.getWidgetRef('dialogEditForm')?.handleClose();\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"button21984\\\"\\r\\n            },\\r\\n            {\\r\\n              \\\"type\\\": \\\"button\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"btnReset\\\",\\r\\n                \\\"label\\\": \\\"common.reset\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": 75,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"type\\\": \\\"warning\\\",\\r\\n                \\\"text\\\": false,\\r\\n                \\\"plain\\\": false,\\r\\n                \\\"round\\\": false,\\r\\n                \\\"circle\\\": false,\\r\\n                \\\"customClass\\\": [],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": null,\\r\\n                \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"//获取表单数据\\\\r\\\\nconst parentRef = this.getFormRef().parentFormRef;\\\\r\\\\nsetTimeout(async () => {\\\\r\\\\n  this._formdata = await parentRef.getWidgetRef('dialogEditForm')?.getValue(false);\\\\r\\\\n}, 1000)\\\\r\\\\n\\\\r\\\\n\\\",\\r\\n                \\\"onClick\\\": \\\"const parentRef= this.getFormRef().parentFormRef;\\\\r\\\\nparentRef.getWidgetRef('dialogEditForm')?.refresh(this._formdata);\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"button84251\\\"\\r\\n            },\\r\\n            {\\r\\n              \\\"type\\\": \\\"button\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n              \\\"formItemFlag\\\": false,\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"button34967\\\",\\r\\n                \\\"label\\\": \\\"common.submit\\\",\\r\\n                \\\"labelAlign\\\": \\\"\\\",\\r\\n                \\\"columnWidth\\\": 75,\\r\\n                \\\"size\\\": \\\"\\\",\\r\\n                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                \\\"disabled\\\": false,\\r\\n                \\\"hidden\\\": true,\\r\\n                \\\"type\\\": \\\"primary\\\",\\r\\n                \\\"text\\\": false,\\r\\n                \\\"plain\\\": false,\\r\\n                \\\"round\\\": false,\\r\\n                \\\"circle\\\": false,\\r\\n                \\\"customClass\\\": [],\\r\\n                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                \\\"labelBold\\\": false,\\r\\n                \\\"labelItalic\\\": false,\\r\\n                \\\"labelUnderline\\\": false,\\r\\n                \\\"labelLineThrough\\\": false,\\r\\n                \\\"labelIconClass\\\": null,\\r\\n                \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                \\\"labelTooltip\\\": null,\\r\\n                \\\"labelIconType\\\": \\\"\\\",\\r\\n                \\\"onCreated\\\": \\\"\\\",\\r\\n                \\\"onMounted\\\": \\\"\\\",\\r\\n                \\\"onClick\\\": \\\"const parentRef= this.getFormRef().parentFormRef;\\\\r\\\\nparentRef.getWidgetRef('dialogEditForm')?.submit();\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"button34967\\\"\\r\\n            }\\r\\n          ],\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"dialogfooter31118\\\",\\r\\n            \\\"columnWidth\\\": 80,\\r\\n            \\\"columnHeight\\\": 35,\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"form\\\": {\\r\\n              \\\"size\\\": \\\"small\\\",\\r\\n              \\\"labelPosition\\\": \\\"left\\\",\\r\\n              \\\"labelAlign\\\": \\\"label-left-align\\\",\\r\\n              \\\"labelWidth\\\": 80,\\r\\n              \\\"customClass\\\": [\\r\\n                \\\"flex\\\",\\r\\n                \\\"justify-end\\\"\\r\\n              ]\\r\\n            },\\r\\n            \\\"label\\\": \\\"dialog-footer\\\"\\r\\n          },\\r\\n          \\\"id\\\": \\\"dialogfooter31118\\\"\\r\\n        },\\r\\n        {\\r\\n          \\\"type\\\": \\\"dialog-body\\\",\\r\\n          \\\"category\\\": \\\"container\\\",\\r\\n          \\\"internal\\\": true,\\r\\n          \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n          \\\"widgetList\\\": [\\r\\n            {\\r\\n              \\\"key\\\": 92283,\\r\\n              \\\"type\\\": \\\"grid\\\",\\r\\n              \\\"category\\\": \\\"container\\\",\\r\\n              \\\"icon\\\": \\\"ep:grid\\\",\\r\\n              \\\"cols\\\": [\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"input\\\",\\r\\n                      \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"MATERIAL\\\",\\r\\n                        \\\"label\\\": \\\"WMSInventory.MATERIAL\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"type\\\": \\\"text\\\",\\r\\n                        \\\"defaultValue\\\": \\\"\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"clearable\\\": true,\\r\\n                        \\\"showPassword\\\": false,\\r\\n                        \\\"required\\\": true,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"validation\\\": [\\r\\n                          \\\"noBlankStart\\\",\\r\\n                          \\\"noBlankEnd\\\"\\r\\n                        ],\\r\\n                        \\\"validationHint\\\": \\\"\\\",\\r\\n                        \\\"formatter\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": [],\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"minLength\\\": 0,\\r\\n                        \\\"maxLength\\\": 50,\\r\\n                        \\\"showWordLimit\\\": true,\\r\\n                        \\\"prefixIcon\\\": \\\"\\\",\\r\\n                        \\\"suffixIcon\\\": \\\"\\\",\\r\\n                        \\\"appendButton\\\": false,\\r\\n                        \\\"appendButtonDisabled\\\": false,\\r\\n                        \\\"buttonIcon\\\": \\\"\\\",\\r\\n                        \\\"iconType\\\": \\\"\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onInput\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"input45172\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol89625\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol89625\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"textarea\\\",\\r\\n                      \\\"icon\\\": \\\"bi:textarea-resize\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"MATERIAL_DESCRIPTION\\\",\\r\\n                        \\\"label\\\": \\\"WMSInventory.MATERIAL_DESCRIPTION\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"rows\\\": 3,\\r\\n                        \\\"defaultValue\\\": \\\"\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"required\\\": false,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"validation\\\": \\\"\\\",\\r\\n                        \\\"validationHint\\\": \\\"\\\",\\r\\n                        \\\"formatter\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": \\\"\\\",\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"minLength\\\": 0,\\r\\n                        \\\"maxLength\\\": 100,\\r\\n                        \\\"showWordLimit\\\": true,\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onInput\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"textarea27889\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol80661\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol80661\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"select\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:select-field\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"FILTERTAGS\\\",\\r\\n                        \\\"label\\\": \\\"FilterTag.FilterTag\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"defaultValue\\\": \\\"\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"clearable\\\": true,\\r\\n                        \\\"filterable\\\": true,\\r\\n                        \\\"allowCreate\\\": false,\\r\\n                        \\\"remote\\\": false,\\r\\n                        \\\"multiple\\\": false,\\r\\n                        \\\"multipleLimit\\\": 0,\\r\\n                        \\\"lazy\\\": false,\\r\\n                        \\\"optionItems\\\": [\\r\\n                          {\\r\\n                            \\\"label\\\": \\\"select 1\\\",\\r\\n                            \\\"value\\\": \\\"1\\\"\\r\\n                          },\\r\\n                          {\\r\\n                            \\\"label\\\": \\\"select 2\\\",\\r\\n                            \\\"value\\\": \\\"2\\\"\\r\\n                          },\\r\\n                          {\\r\\n                            \\\"label\\\": \\\"select 3\\\",\\r\\n                            \\\"value\\\": \\\"3\\\"\\r\\n                          }\\r\\n                        ],\\r\\n                        \\\"required\\\": false,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"dataSource\\\": \\\"FILTERTAG\\\",\\r\\n                        \\\"optionTagName\\\": \\\"key\\\",\\r\\n                        \\\"optionValueName\\\": \\\"value\\\",\\r\\n                        \\\"trigger\\\": null,\\r\\n                        \\\"formatter\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": \\\"\\\",\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onRemoteQuery\\\": \\\"\\\",\\r\\n                        \\\"onRemoteQueryBefore\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"select103174\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol36277\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol36277\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"number\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:number-field\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"MENGE\\\",\\r\\n                        \\\"label\\\": \\\"WMSInventory.MENGE\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"defaultValue\\\": null,\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"required\\\": true,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": \\\"\\\",\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"min\\\": -10000000000,\\r\\n                        \\\"max\\\": 100000000000,\\r\\n                        \\\"precision\\\": 2,\\r\\n                        \\\"step\\\": 1,\\r\\n                        \\\"controlsPosition\\\": \\\"\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"number86330\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol18428\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol18428\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"input\\\",\\r\\n                      \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"UNIT\\\",\\r\\n                        \\\"label\\\": \\\"WMSInventory.UNIT\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"type\\\": \\\"text\\\",\\r\\n                        \\\"defaultValue\\\": \\\"\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"clearable\\\": true,\\r\\n                        \\\"showPassword\\\": false,\\r\\n                        \\\"required\\\": false,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"validation\\\": [\\r\\n                          \\\"noBlankStart\\\",\\r\\n                          \\\"noBlankEnd\\\"\\r\\n                        ],\\r\\n                        \\\"validationHint\\\": \\\"\\\",\\r\\n                        \\\"formatter\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": \\\"\\\",\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"minLength\\\": 0,\\r\\n                        \\\"maxLength\\\": 6,\\r\\n                        \\\"showWordLimit\\\": true,\\r\\n                        \\\"prefixIcon\\\": \\\"\\\",\\r\\n                        \\\"suffixIcon\\\": \\\"\\\",\\r\\n                        \\\"appendButton\\\": false,\\r\\n                        \\\"appendButtonDisabled\\\": false,\\r\\n                        \\\"buttonIcon\\\": \\\"\\\",\\r\\n                        \\\"iconType\\\": \\\"\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onInput\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"input72157\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol71331\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol71331\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"number\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:number-field\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"ONLINE_QTY\\\",\\r\\n                        \\\"label\\\": \\\"WMSInventory.ONLINE_QTY\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"defaultValue\\\": null,\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": true,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"required\\\": false,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": \\\"\\\",\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"min\\\": -10000000000,\\r\\n                        \\\"max\\\": 100000000000,\\r\\n                        \\\"precision\\\": 0,\\r\\n                        \\\"step\\\": 1,\\r\\n                        \\\"controlsPosition\\\": \\\"\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"number66265\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol67121\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol67121\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"input\\\",\\r\\n                      \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"MO_LOT_NUMBER\\\",\\r\\n                        \\\"label\\\": \\\"WMSPickingDetail.MO_LOT_NUMBER\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"type\\\": \\\"text\\\",\\r\\n                        \\\"defaultValue\\\": \\\"\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"clearable\\\": true,\\r\\n                        \\\"showPassword\\\": false,\\r\\n                        \\\"required\\\": false,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"validation\\\": [\\r\\n                          \\\"noBlankStart\\\",\\r\\n                          \\\"noBlankEnd\\\"\\r\\n                        ],\\r\\n                        \\\"validationHint\\\": \\\"\\\",\\r\\n                        \\\"formatter\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": \\\"\\\",\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"minLength\\\": 0,\\r\\n                        \\\"maxLength\\\": 50,\\r\\n                        \\\"showWordLimit\\\": true,\\r\\n                        \\\"prefixIcon\\\": \\\"\\\",\\r\\n                        \\\"suffixIcon\\\": \\\"\\\",\\r\\n                        \\\"appendButton\\\": false,\\r\\n                        \\\"appendButtonDisabled\\\": false,\\r\\n                        \\\"buttonIcon\\\": \\\"\\\",\\r\\n                        \\\"iconType\\\": \\\"\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onInput\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"input109636\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol76212\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol76212\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"input\\\",\\r\\n                      \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"SO_LOT_NUMBER\\\",\\r\\n                        \\\"label\\\": \\\"WMSPickingDetail.SO_LOT_NUMBER\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"type\\\": \\\"text\\\",\\r\\n                        \\\"defaultValue\\\": \\\"\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"clearable\\\": true,\\r\\n                        \\\"showPassword\\\": false,\\r\\n                        \\\"required\\\": false,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"validation\\\": [\\r\\n                          \\\"noBlankStart\\\",\\r\\n                          \\\"noBlankEnd\\\"\\r\\n                        ],\\r\\n                        \\\"validationHint\\\": \\\"\\\",\\r\\n                        \\\"formatter\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": \\\"\\\",\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"minLength\\\": 0,\\r\\n                        \\\"maxLength\\\": 300,\\r\\n                        \\\"showWordLimit\\\": true,\\r\\n                        \\\"prefixIcon\\\": \\\"\\\",\\r\\n                        \\\"suffixIcon\\\": \\\"\\\",\\r\\n                        \\\"appendButton\\\": false,\\r\\n                        \\\"appendButtonDisabled\\\": false,\\r\\n                        \\\"buttonIcon\\\": \\\"\\\",\\r\\n                        \\\"iconType\\\": \\\"\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onInput\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"input79753\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol99965\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol99965\\\"\\r\\n                },\\r\\n                {\\r\\n                  \\\"type\\\": \\\"grid-col\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"grid-col\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"type\\\": \\\"input\\\",\\r\\n                      \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                      \\\"formItemFlag\\\": true,\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"SUPPLIER_NAME\\\",\\r\\n                        \\\"label\\\": \\\"WMSPickingDetail.SUPPLIER_NAME\\\",\\r\\n                        \\\"labelAlign\\\": \\\"label-right-align\\\",\\r\\n                        \\\"type\\\": \\\"text\\\",\\r\\n                        \\\"defaultValue\\\": \\\"\\\",\\r\\n                        \\\"displayStyle\\\": \\\"block\\\",\\r\\n                        \\\"placeholder\\\": \\\"\\\",\\r\\n                        \\\"columnWidth\\\": 100,\\r\\n                        \\\"size\\\": \\\"\\\",\\r\\n                        \\\"labelWidth\\\": null,\\r\\n                        \\\"labelHidden\\\": false,\\r\\n                        \\\"readonly\\\": false,\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"clearable\\\": true,\\r\\n                        \\\"showPassword\\\": false,\\r\\n                        \\\"required\\\": false,\\r\\n                        \\\"requiredHint\\\": \\\"\\\",\\r\\n                        \\\"validation\\\": [\\r\\n                          \\\"noBlankStart\\\",\\r\\n                          \\\"noBlankEnd\\\"\\r\\n                        ],\\r\\n                        \\\"validationHint\\\": \\\"\\\",\\r\\n                        \\\"formatter\\\": \\\"\\\",\\r\\n                        \\\"columnFixed\\\": false,\\r\\n                        \\\"columnFiltering\\\": false,\\r\\n                        \\\"columnSorting\\\": false,\\r\\n                        \\\"columnSortingType\\\": \\\"\\\",\\r\\n                        \\\"customClass\\\": \\\"\\\",\\r\\n                        \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                        \\\"labelFontSize\\\": \\\"\\\",\\r\\n                        \\\"labelBold\\\": false,\\r\\n                        \\\"labelItalic\\\": false,\\r\\n                        \\\"labelUnderline\\\": false,\\r\\n                        \\\"labelLineThrough\\\": false,\\r\\n                        \\\"labelIconClass\\\": null,\\r\\n                        \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                        \\\"labelTooltip\\\": null,\\r\\n                        \\\"labelIconType\\\": \\\"\\\",\\r\\n                        \\\"minLength\\\": 0,\\r\\n                        \\\"maxLength\\\": 100,\\r\\n                        \\\"showWordLimit\\\": true,\\r\\n                        \\\"prefixIcon\\\": \\\"\\\",\\r\\n                        \\\"suffixIcon\\\": \\\"\\\",\\r\\n                        \\\"appendButton\\\": false,\\r\\n                        \\\"appendButtonDisabled\\\": false,\\r\\n                        \\\"buttonIcon\\\": \\\"\\\",\\r\\n                        \\\"iconType\\\": \\\"\\\",\\r\\n                        \\\"onCreated\\\": \\\"\\\",\\r\\n                        \\\"onMounted\\\": \\\"\\\",\\r\\n                        \\\"onInput\\\": \\\"\\\",\\r\\n                        \\\"onChange\\\": \\\"\\\",\\r\\n                        \\\"onFocus\\\": \\\"\\\",\\r\\n                        \\\"onBlur\\\": \\\"\\\",\\r\\n                        \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                        \\\"onValidate\\\": \\\"\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"input66787\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"gridcol4765\\\",\\r\\n                    \\\"hidden\\\": false,\\r\\n                    \\\"span\\\": 8,\\r\\n                    \\\"offset\\\": 0,\\r\\n                    \\\"push\\\": 0,\\r\\n                    \\\"pull\\\": 0,\\r\\n                    \\\"responsive\\\": false,\\r\\n                    \\\"md\\\": 12,\\r\\n                    \\\"sm\\\": 12,\\r\\n                    \\\"xs\\\": 12,\\r\\n                    \\\"customClass\\\": \\\"\\\",\\r\\n                    \\\"onCreated\\\": \\\"\\\",\\r\\n                    \\\"onMounted\\\": \\\"\\\"\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"gridcol4765\\\"\\r\\n                }\\r\\n              ],\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"gridForm\\\",\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"gutter\\\": 12,\\r\\n                \\\"colHeight\\\": null\\r\\n              },\\r\\n              \\\"id\\\": \\\"grid100462\\\"\\r\\n            },\\r\\n            {\\r\\n              \\\"type\\\": \\\"tab\\\",\\r\\n              \\\"category\\\": \\\"container\\\",\\r\\n              \\\"icon\\\": \\\"svg-icon:tab\\\",\\r\\n              \\\"tabs\\\": [\\r\\n                {\\r\\n                  \\\"type\\\": \\\"tab-pane\\\",\\r\\n                  \\\"category\\\": \\\"container\\\",\\r\\n                  \\\"icon\\\": \\\"tab-pane\\\",\\r\\n                  \\\"internal\\\": true,\\r\\n                  \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                  \\\"widgetList\\\": [\\r\\n                    {\\r\\n                      \\\"key\\\": 79014,\\r\\n                      \\\"type\\\": \\\"grid\\\",\\r\\n                      \\\"category\\\": \\\"container\\\",\\r\\n                      \\\"icon\\\": \\\"ep:grid\\\",\\r\\n                      \\\"cols\\\": [\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"grid-col\\\",\\r\\n                          \\\"category\\\": \\\"container\\\",\\r\\n                          \\\"icon\\\": \\\"grid-col\\\",\\r\\n                          \\\"internal\\\": true,\\r\\n                          \\\"draginable\\\": \\\"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\\\",\\r\\n                          \\\"widgetList\\\": [\\r\\n                            {\\r\\n                              \\\"type\\\": \\\"button\\\",\\r\\n                              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n                              \\\"formItemFlag\\\": false,\\r\\n                              \\\"options\\\": {\\r\\n                                \\\"name\\\": \\\"btnAddDetail_VM_INVENTORY_DETAILs\\\",\\r\\n                                \\\"label\\\": \\\"common.add\\\",\\r\\n                                \\\"labelAlign\\\": \\\"\\\",\\r\\n                                \\\"columnWidth\\\": null,\\r\\n                                \\\"size\\\": \\\"small\\\",\\r\\n                                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                                \\\"disabled\\\": false,\\r\\n                                \\\"hidden\\\": false,\\r\\n                                \\\"type\\\": \\\"success\\\",\\r\\n                                \\\"text\\\": false,\\r\\n                                \\\"plain\\\": false,\\r\\n                                \\\"round\\\": false,\\r\\n                                \\\"circle\\\": false,\\r\\n                                \\\"customClass\\\": [\\r\\n                                  \\\"pl-2\\\"\\r\\n                                ],\\r\\n                                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                                \\\"labelBold\\\": false,\\r\\n                                \\\"labelItalic\\\": false,\\r\\n                                \\\"labelUnderline\\\": false,\\r\\n                                \\\"labelLineThrough\\\": false,\\r\\n                                \\\"labelIconClass\\\": \\\"Plus\\\",\\r\\n                                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                                \\\"labelTooltip\\\": null,\\r\\n                                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                                \\\"onCreated\\\": \\\"this.detailName='VM_INVENTORY_DETAILs'\\\",\\r\\n                                \\\"onMounted\\\": \\\"\\\",\\r\\n                                \\\"onClick\\\": \\\"//生成一行默认值\\\\n(this.getWidgetRef(this.detailName)).addToTableData();\\\"\\r\\n                              },\\r\\n                              \\\"id\\\": \\\"button36860\\\"\\r\\n                            },\\r\\n                            {\\r\\n                              \\\"type\\\": \\\"button\\\",\\r\\n                              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n                              \\\"formItemFlag\\\": false,\\r\\n                              \\\"options\\\": {\\r\\n                                \\\"name\\\": \\\"btnDelDetail_VM_INVENTORY_DETAILs\\\",\\r\\n                                \\\"label\\\": \\\"common.delete\\\",\\r\\n                                \\\"labelAlign\\\": \\\"\\\",\\r\\n                                \\\"columnWidth\\\": null,\\r\\n                                \\\"size\\\": \\\"small\\\",\\r\\n                                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                                \\\"disabled\\\": false,\\r\\n                                \\\"hidden\\\": false,\\r\\n                                \\\"type\\\": \\\"danger\\\",\\r\\n                                \\\"text\\\": false,\\r\\n                                \\\"plain\\\": false,\\r\\n                                \\\"round\\\": false,\\r\\n                                \\\"circle\\\": false,\\r\\n                                \\\"customClass\\\": [\\r\\n                                  \\\"pl-2\\\"\\r\\n                                ],\\r\\n                                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                                \\\"labelBold\\\": false,\\r\\n                                \\\"labelItalic\\\": false,\\r\\n                                \\\"labelUnderline\\\": false,\\r\\n                                \\\"labelLineThrough\\\": false,\\r\\n                                \\\"labelIconClass\\\": \\\"Minus\\\",\\r\\n                                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                                \\\"labelTooltip\\\": null,\\r\\n                                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                                \\\"onCreated\\\": \\\"this.detailName='VM_INVENTORY_DETAILs'\\\",\\r\\n                                \\\"onMounted\\\": \\\"\\\",\\r\\n                                \\\"onClick\\\": \\\"const rows = (this.getWidgetRef(this.detailName)).getSelectionRows();\\\\nif (!rows || rows.length < 1) {\\\\n  return useMessage.error(this.$t('common.selectNoData', [this.$t('common.delete')]));\\\\n}\\\\nrows.forEach(row => {\\\\n  (this.getWidgetRef(this.detailName)).deleteTableData(row);\\\\n})\\\"\\r\\n                              },\\r\\n                              \\\"id\\\": \\\"button42081\\\"\\r\\n                            },\\r\\n                            {\\r\\n                              \\\"type\\\": \\\"button\\\",\\r\\n                              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n                              \\\"formItemFlag\\\": false,\\r\\n                              \\\"options\\\": {\\r\\n                                \\\"name\\\": \\\"btnRefreshDetail_VM_INVENTORY_DETAILs\\\",\\r\\n                                \\\"label\\\": \\\"common.refresh\\\",\\r\\n                                \\\"labelAlign\\\": \\\"\\\",\\r\\n                                \\\"columnWidth\\\": null,\\r\\n                                \\\"size\\\": \\\"small\\\",\\r\\n                                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                                \\\"disabled\\\": false,\\r\\n                                \\\"hidden\\\": false,\\r\\n                                \\\"type\\\": \\\"primary\\\",\\r\\n                                \\\"text\\\": false,\\r\\n                                \\\"plain\\\": true,\\r\\n                                \\\"round\\\": false,\\r\\n                                \\\"circle\\\": false,\\r\\n                                \\\"customClass\\\": [\\r\\n                                  \\\"pl-2\\\"\\r\\n                                ],\\r\\n                                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                                \\\"labelBold\\\": false,\\r\\n                                \\\"labelItalic\\\": false,\\r\\n                                \\\"labelUnderline\\\": false,\\r\\n                                \\\"labelLineThrough\\\": false,\\r\\n                                \\\"labelIconClass\\\": \\\"Refresh\\\",\\r\\n                                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                                \\\"labelTooltip\\\": null,\\r\\n                                \\\"labelIconType\\\": \\\"pl\\\",\\r\\n                                \\\"onCreated\\\": \\\"this.detailName='VM_INVENTORY_DETAILs'\\\",\\r\\n                                \\\"onMounted\\\": \\\"\\\",\\r\\n                                \\\"onClick\\\": \\\"//表格的重新加载\\\\n(this.getWidgetRef(this.detailName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/true)\\\\n\\\"\\r\\n                              },\\r\\n                              \\\"id\\\": \\\"button119515\\\"\\r\\n                            },\\r\\n                            {\\r\\n                              \\\"type\\\": \\\"button\\\",\\r\\n                              \\\"icon\\\": \\\"svg-icon:button\\\",\\r\\n                              \\\"formItemFlag\\\": false,\\r\\n                              \\\"options\\\": {\\r\\n                                \\\"name\\\": \\\"btnAdjustDetail_VM_INVENTORY_DETAILs\\\",\\r\\n                                \\\"label\\\": \\\"common.adjustColumns\\\",\\r\\n                                \\\"labelAlign\\\": \\\"\\\",\\r\\n                                \\\"columnWidth\\\": null,\\r\\n                                \\\"size\\\": \\\"small\\\",\\r\\n                                \\\"displayStyle\\\": \\\"inline-flex\\\",\\r\\n                                \\\"disabled\\\": false,\\r\\n                                \\\"hidden\\\": false,\\r\\n                                \\\"type\\\": \\\"warning\\\",\\r\\n                                \\\"text\\\": false,\\r\\n                                \\\"plain\\\": true,\\r\\n                                \\\"round\\\": false,\\r\\n                                \\\"circle\\\": false,\\r\\n                                \\\"customClass\\\": [\\r\\n                                  \\\"pl-2\\\"\\r\\n                                ],\\r\\n                                \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                                \\\"labelFontSize\\\": \\\"\\\",\\r\\n                                \\\"labelBold\\\": false,\\r\\n                                \\\"labelItalic\\\": false,\\r\\n                                \\\"labelUnderline\\\": false,\\r\\n                                \\\"labelLineThrough\\\": false,\\r\\n                                \\\"labelIconClass\\\": \\\"el-icon-set-up\\\",\\r\\n                                \\\"labelIconPosition\\\": \\\"front\\\",\\r\\n                                \\\"labelTooltip\\\": null,\\r\\n                                \\\"labelIconType\\\": \\\"el\\\",\\r\\n                                \\\"onCreated\\\": \\\"this.detailName='VM_INVENTORY_DETAILs'\\\",\\r\\n                                \\\"onMounted\\\": \\\"\\\",\\r\\n                                \\\"onClick\\\": \\\"//表格的列调整\\\\nconst $table = this.getWidgetRef(this.detailName);\\\\n$table.openColumnDrawer();\\\\n\\\"\\r\\n                              },\\r\\n                              \\\"id\\\": \\\"button106086\\\"\\r\\n                            }\\r\\n                          ],\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"gridcol112520\\\",\\r\\n                            \\\"hidden\\\": true,\\r\\n                            \\\"span\\\": 24,\\r\\n                            \\\"offset\\\": 0,\\r\\n                            \\\"push\\\": 0,\\r\\n                            \\\"pull\\\": 0,\\r\\n                            \\\"responsive\\\": false,\\r\\n                            \\\"md\\\": 12,\\r\\n                            \\\"sm\\\": 12,\\r\\n                            \\\"xs\\\": 12,\\r\\n                            \\\"customClass\\\": [\\r\\n                              \\\"flex\\\",\\r\\n                              \\\"justify-end\\\"\\r\\n                            ]\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"gridcol106232\\\"\\r\\n                        }\\r\\n                      ],\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"grid115896\\\",\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"gutter\\\": 12,\\r\\n                        \\\"colHeight\\\": null,\\r\\n                        \\\"customClass\\\": [\\r\\n                          \\\"mb-2\\\"\\r\\n                        ]\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"grid54404\\\"\\r\\n                    },\\r\\n                    {\\r\\n                      \\\"key\\\": 27644,\\r\\n                      \\\"type\\\": \\\"data-table\\\",\\r\\n                      \\\"category\\\": \\\"container\\\",\\r\\n                      \\\"icon\\\": \\\"svg-icon:data-table\\\",\\r\\n                      \\\"draginable\\\": \\\"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\\\",\\r\\n                      \\\"widgetList\\\": [\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"input\\\",\\r\\n                          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                          \\\"formItemFlag\\\": true,\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"系统\\\",\\r\\n                            \\\"label\\\": \\\"系统\\\",\\r\\n                            \\\"labelAlign\\\": \\\"label-center-align\\\",\\r\\n                            \\\"type\\\": \\\"text\\\",\\r\\n                            \\\"defaultValue\\\": \\\"\\\",\\r\\n                            \\\"displayStyle\\\": \\\"block\\\",\\r\\n                            \\\"placeholder\\\": \\\"\\\",\\r\\n                            \\\"columnWidth\\\": 110,\\r\\n                            \\\"size\\\": \\\"\\\",\\r\\n                            \\\"labelWidth\\\": null,\\r\\n                            \\\"labelHidden\\\": false,\\r\\n                            \\\"readonly\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"clearable\\\": true,\\r\\n                            \\\"showPassword\\\": false,\\r\\n                            \\\"required\\\": true,\\r\\n                            \\\"requiredHint\\\": \\\"\\\",\\r\\n                            \\\"validation\\\": [\\r\\n                              \\\"noBlankStart\\\",\\r\\n                              \\\"noBlankEnd\\\"\\r\\n                            ],\\r\\n                            \\\"validationHint\\\": \\\"\\\",\\r\\n                            \\\"formatter\\\": \\\"\\\",\\r\\n                            \\\"columnFixed\\\": false,\\r\\n                            \\\"columnFiltering\\\": false,\\r\\n                            \\\"columnSorting\\\": false,\\r\\n                            \\\"columnSortingType\\\": \\\"\\\",\\r\\n                            \\\"customClass\\\": \\\"\\\",\\r\\n                            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                            \\\"labelFontSize\\\": \\\"\\\",\\r\\n                            \\\"labelBold\\\": false,\\r\\n                            \\\"labelItalic\\\": false,\\r\\n                            \\\"labelUnderline\\\": false,\\r\\n                            \\\"labelLineThrough\\\": false,\\r\\n                            \\\"labelIconClass\\\": null,\\r\\n                            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                            \\\"labelTooltip\\\": null,\\r\\n                            \\\"labelIconType\\\": \\\"\\\",\\r\\n                            \\\"minLength\\\": 0,\\r\\n                            \\\"maxLength\\\": 6,\\r\\n                            \\\"showWordLimit\\\": false,\\r\\n                            \\\"prefixIcon\\\": \\\"\\\",\\r\\n                            \\\"suffixIcon\\\": \\\"\\\",\\r\\n                            \\\"appendButton\\\": false,\\r\\n                            \\\"appendButtonDisabled\\\": false,\\r\\n                            \\\"buttonIcon\\\": \\\"\\\",\\r\\n                            \\\"iconType\\\": \\\"\\\",\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"onInput\\\": \\\"\\\",\\r\\n                            \\\"onChange\\\": \\\"\\\",\\r\\n                            \\\"onFocus\\\": \\\"\\\",\\r\\n                            \\\"onBlur\\\": \\\"\\\",\\r\\n                            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                            \\\"onValidate\\\": \\\"\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"input31184\\\"\\r\\n                        },\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"input\\\",\\r\\n                          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                          \\\"formItemFlag\\\": true,\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"动作\\\",\\r\\n                            \\\"label\\\": \\\"动作\\\",\\r\\n                            \\\"labelAlign\\\": \\\"label-center-align\\\",\\r\\n                            \\\"type\\\": \\\"text\\\",\\r\\n                            \\\"defaultValue\\\": \\\"\\\",\\r\\n                            \\\"displayStyle\\\": \\\"block\\\",\\r\\n                            \\\"placeholder\\\": \\\"\\\",\\r\\n                            \\\"columnWidth\\\": 110,\\r\\n                            \\\"size\\\": \\\"\\\",\\r\\n                            \\\"labelWidth\\\": null,\\r\\n                            \\\"labelHidden\\\": false,\\r\\n                            \\\"readonly\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"clearable\\\": true,\\r\\n                            \\\"showPassword\\\": false,\\r\\n                            \\\"required\\\": true,\\r\\n                            \\\"requiredHint\\\": \\\"\\\",\\r\\n                            \\\"validation\\\": [\\r\\n                              \\\"noBlankStart\\\",\\r\\n                              \\\"noBlankEnd\\\"\\r\\n                            ],\\r\\n                            \\\"validationHint\\\": \\\"\\\",\\r\\n                            \\\"formatter\\\": \\\"\\\",\\r\\n                            \\\"columnFixed\\\": false,\\r\\n                            \\\"columnFiltering\\\": false,\\r\\n                            \\\"columnSorting\\\": false,\\r\\n                            \\\"columnSortingType\\\": \\\"\\\",\\r\\n                            \\\"customClass\\\": \\\"\\\",\\r\\n                            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                            \\\"labelFontSize\\\": \\\"\\\",\\r\\n                            \\\"labelBold\\\": false,\\r\\n                            \\\"labelItalic\\\": false,\\r\\n                            \\\"labelUnderline\\\": false,\\r\\n                            \\\"labelLineThrough\\\": false,\\r\\n                            \\\"labelIconClass\\\": null,\\r\\n                            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                            \\\"labelTooltip\\\": null,\\r\\n                            \\\"labelIconType\\\": \\\"\\\",\\r\\n                            \\\"minLength\\\": 0,\\r\\n                            \\\"maxLength\\\": 4,\\r\\n                            \\\"showWordLimit\\\": false,\\r\\n                            \\\"prefixIcon\\\": \\\"\\\",\\r\\n                            \\\"suffixIcon\\\": \\\"\\\",\\r\\n                            \\\"appendButton\\\": false,\\r\\n                            \\\"appendButtonDisabled\\\": false,\\r\\n                            \\\"buttonIcon\\\": \\\"\\\",\\r\\n                            \\\"iconType\\\": \\\"\\\",\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"onInput\\\": \\\"\\\",\\r\\n                            \\\"onChange\\\": \\\"\\\",\\r\\n                            \\\"onFocus\\\": \\\"\\\",\\r\\n                            \\\"onBlur\\\": \\\"\\\",\\r\\n                            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                            \\\"onValidate\\\": \\\"\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"input81645\\\"\\r\\n                        },\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"input\\\",\\r\\n                          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                          \\\"formItemFlag\\\": true,\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"排序\\\",\\r\\n                            \\\"label\\\": \\\"ColumnAdjust.Order\\\",\\r\\n                            \\\"labelAlign\\\": \\\"label-center-align\\\",\\r\\n                            \\\"type\\\": \\\"text\\\",\\r\\n                            \\\"defaultValue\\\": \\\"\\\",\\r\\n                            \\\"displayStyle\\\": \\\"block\\\",\\r\\n                            \\\"placeholder\\\": \\\"\\\",\\r\\n                            \\\"columnWidth\\\": 110,\\r\\n                            \\\"size\\\": \\\"\\\",\\r\\n                            \\\"labelWidth\\\": null,\\r\\n                            \\\"labelHidden\\\": false,\\r\\n                            \\\"readonly\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"clearable\\\": true,\\r\\n                            \\\"showPassword\\\": false,\\r\\n                            \\\"required\\\": false,\\r\\n                            \\\"requiredHint\\\": \\\"\\\",\\r\\n                            \\\"validation\\\": [\\r\\n                              \\\"noBlankStart\\\",\\r\\n                              \\\"noBlankEnd\\\"\\r\\n                            ],\\r\\n                            \\\"validationHint\\\": \\\"\\\",\\r\\n                            \\\"formatter\\\": \\\"\\\",\\r\\n                            \\\"columnFixed\\\": false,\\r\\n                            \\\"columnFiltering\\\": false,\\r\\n                            \\\"columnSorting\\\": false,\\r\\n                            \\\"columnSortingType\\\": \\\"\\\",\\r\\n                            \\\"customClass\\\": \\\"\\\",\\r\\n                            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                            \\\"labelFontSize\\\": \\\"\\\",\\r\\n                            \\\"labelBold\\\": false,\\r\\n                            \\\"labelItalic\\\": false,\\r\\n                            \\\"labelUnderline\\\": false,\\r\\n                            \\\"labelLineThrough\\\": false,\\r\\n                            \\\"labelIconClass\\\": null,\\r\\n                            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                            \\\"labelTooltip\\\": null,\\r\\n                            \\\"labelIconType\\\": \\\"\\\",\\r\\n                            \\\"minLength\\\": 0,\\r\\n                            \\\"maxLength\\\": 8,\\r\\n                            \\\"showWordLimit\\\": false,\\r\\n                            \\\"prefixIcon\\\": \\\"\\\",\\r\\n                            \\\"suffixIcon\\\": \\\"\\\",\\r\\n                            \\\"appendButton\\\": false,\\r\\n                            \\\"appendButtonDisabled\\\": false,\\r\\n                            \\\"buttonIcon\\\": \\\"\\\",\\r\\n                            \\\"iconType\\\": \\\"\\\",\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"onInput\\\": \\\"\\\",\\r\\n                            \\\"onChange\\\": \\\"\\\",\\r\\n                            \\\"onFocus\\\": \\\"\\\",\\r\\n                            \\\"onBlur\\\": \\\"\\\",\\r\\n                            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                            \\\"onValidate\\\": \\\"\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"input28963\\\"\\r\\n                        },\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"input\\\",\\r\\n                          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                          \\\"formItemFlag\\\": true,\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"日期\\\",\\r\\n                            \\\"label\\\": \\\"日期\\\",\\r\\n                            \\\"labelAlign\\\": \\\"label-center-align\\\",\\r\\n                            \\\"type\\\": \\\"text\\\",\\r\\n                            \\\"defaultValue\\\": \\\"\\\",\\r\\n                            \\\"displayStyle\\\": \\\"block\\\",\\r\\n                            \\\"placeholder\\\": \\\"\\\",\\r\\n                            \\\"columnWidth\\\": 110,\\r\\n                            \\\"size\\\": \\\"\\\",\\r\\n                            \\\"labelWidth\\\": null,\\r\\n                            \\\"labelHidden\\\": false,\\r\\n                            \\\"readonly\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"clearable\\\": true,\\r\\n                            \\\"showPassword\\\": false,\\r\\n                            \\\"required\\\": false,\\r\\n                            \\\"requiredHint\\\": \\\"\\\",\\r\\n                            \\\"validation\\\": [\\r\\n                              \\\"noBlankStart\\\",\\r\\n                              \\\"noBlankEnd\\\"\\r\\n                            ],\\r\\n                            \\\"validationHint\\\": \\\"\\\",\\r\\n                            \\\"formatter\\\": \\\"\\\",\\r\\n                            \\\"columnFixed\\\": false,\\r\\n                            \\\"columnFiltering\\\": false,\\r\\n                            \\\"columnSorting\\\": false,\\r\\n                            \\\"columnSortingType\\\": \\\"\\\",\\r\\n                            \\\"customClass\\\": \\\"\\\",\\r\\n                            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                            \\\"labelFontSize\\\": \\\"\\\",\\r\\n                            \\\"labelBold\\\": false,\\r\\n                            \\\"labelItalic\\\": false,\\r\\n                            \\\"labelUnderline\\\": false,\\r\\n                            \\\"labelLineThrough\\\": false,\\r\\n                            \\\"labelIconClass\\\": null,\\r\\n                            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                            \\\"labelTooltip\\\": null,\\r\\n                            \\\"labelIconType\\\": \\\"\\\",\\r\\n                            \\\"minLength\\\": 0,\\r\\n                            \\\"maxLength\\\": 8,\\r\\n                            \\\"showWordLimit\\\": false,\\r\\n                            \\\"prefixIcon\\\": \\\"\\\",\\r\\n                            \\\"suffixIcon\\\": \\\"\\\",\\r\\n                            \\\"appendButton\\\": false,\\r\\n                            \\\"appendButtonDisabled\\\": false,\\r\\n                            \\\"buttonIcon\\\": \\\"\\\",\\r\\n                            \\\"iconType\\\": \\\"\\\",\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"onInput\\\": \\\"\\\",\\r\\n                            \\\"onChange\\\": \\\"\\\",\\r\\n                            \\\"onFocus\\\": \\\"\\\",\\r\\n                            \\\"onBlur\\\": \\\"\\\",\\r\\n                            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                            \\\"onValidate\\\": \\\"\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"input48859\\\"\\r\\n                        },\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"input\\\",\\r\\n                          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                          \\\"formItemFlag\\\": true,\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"单号\\\",\\r\\n                            \\\"label\\\": \\\"WMSCheckingHeader.ORDER_NO\\\",\\r\\n                            \\\"labelAlign\\\": \\\"label-center-align\\\",\\r\\n                            \\\"type\\\": \\\"text\\\",\\r\\n                            \\\"defaultValue\\\": \\\"\\\",\\r\\n                            \\\"displayStyle\\\": \\\"block\\\",\\r\\n                            \\\"placeholder\\\": \\\"\\\",\\r\\n                            \\\"columnWidth\\\": 110,\\r\\n                            \\\"size\\\": \\\"\\\",\\r\\n                            \\\"labelWidth\\\": null,\\r\\n                            \\\"labelHidden\\\": false,\\r\\n                            \\\"readonly\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"clearable\\\": true,\\r\\n                            \\\"showPassword\\\": false,\\r\\n                            \\\"required\\\": true,\\r\\n                            \\\"requiredHint\\\": \\\"\\\",\\r\\n                            \\\"validation\\\": [\\r\\n                              \\\"noBlankStart\\\",\\r\\n                              \\\"noBlankEnd\\\"\\r\\n                            ],\\r\\n                            \\\"validationHint\\\": \\\"\\\",\\r\\n                            \\\"formatter\\\": \\\"\\\",\\r\\n                            \\\"columnFixed\\\": false,\\r\\n                            \\\"columnFiltering\\\": false,\\r\\n                            \\\"columnSorting\\\": false,\\r\\n                            \\\"columnSortingType\\\": \\\"\\\",\\r\\n                            \\\"customClass\\\": \\\"\\\",\\r\\n                            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                            \\\"labelFontSize\\\": \\\"\\\",\\r\\n                            \\\"labelBold\\\": false,\\r\\n                            \\\"labelItalic\\\": false,\\r\\n                            \\\"labelUnderline\\\": false,\\r\\n                            \\\"labelLineThrough\\\": false,\\r\\n                            \\\"labelIconClass\\\": null,\\r\\n                            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                            \\\"labelTooltip\\\": null,\\r\\n                            \\\"labelIconType\\\": \\\"\\\",\\r\\n                            \\\"minLength\\\": 0,\\r\\n                            \\\"maxLength\\\": 50,\\r\\n                            \\\"showWordLimit\\\": false,\\r\\n                            \\\"prefixIcon\\\": \\\"\\\",\\r\\n                            \\\"suffixIcon\\\": \\\"\\\",\\r\\n                            \\\"appendButton\\\": false,\\r\\n                            \\\"appendButtonDisabled\\\": false,\\r\\n                            \\\"buttonIcon\\\": \\\"\\\",\\r\\n                            \\\"iconType\\\": \\\"\\\",\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"onInput\\\": \\\"\\\",\\r\\n                            \\\"onChange\\\": \\\"\\\",\\r\\n                            \\\"onFocus\\\": \\\"\\\",\\r\\n                            \\\"onBlur\\\": \\\"\\\",\\r\\n                            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                            \\\"onValidate\\\": \\\"\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"input40520\\\"\\r\\n                        },\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"input\\\",\\r\\n                          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                          \\\"formItemFlag\\\": true,\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"物料\\\",\\r\\n                            \\\"label\\\": \\\"WMSInventory.MATERIAL\\\",\\r\\n                            \\\"labelAlign\\\": \\\"label-center-align\\\",\\r\\n                            \\\"type\\\": \\\"text\\\",\\r\\n                            \\\"defaultValue\\\": \\\"\\\",\\r\\n                            \\\"displayStyle\\\": \\\"block\\\",\\r\\n                            \\\"placeholder\\\": \\\"\\\",\\r\\n                            \\\"columnWidth\\\": 110,\\r\\n                            \\\"size\\\": \\\"\\\",\\r\\n                            \\\"labelWidth\\\": null,\\r\\n                            \\\"labelHidden\\\": false,\\r\\n                            \\\"readonly\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"clearable\\\": true,\\r\\n                            \\\"showPassword\\\": false,\\r\\n                            \\\"required\\\": false,\\r\\n                            \\\"requiredHint\\\": \\\"\\\",\\r\\n                            \\\"validation\\\": [\\r\\n                              \\\"noBlankStart\\\",\\r\\n                              \\\"noBlankEnd\\\"\\r\\n                            ],\\r\\n                            \\\"validationHint\\\": \\\"\\\",\\r\\n                            \\\"formatter\\\": \\\"\\\",\\r\\n                            \\\"columnFixed\\\": false,\\r\\n                            \\\"columnFiltering\\\": false,\\r\\n                            \\\"columnSorting\\\": false,\\r\\n                            \\\"columnSortingType\\\": \\\"\\\",\\r\\n                            \\\"customClass\\\": \\\"\\\",\\r\\n                            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                            \\\"labelFontSize\\\": \\\"\\\",\\r\\n                            \\\"labelBold\\\": false,\\r\\n                            \\\"labelItalic\\\": false,\\r\\n                            \\\"labelUnderline\\\": false,\\r\\n                            \\\"labelLineThrough\\\": false,\\r\\n                            \\\"labelIconClass\\\": null,\\r\\n                            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                            \\\"labelTooltip\\\": null,\\r\\n                            \\\"labelIconType\\\": \\\"\\\",\\r\\n                            \\\"minLength\\\": 0,\\r\\n                            \\\"maxLength\\\": 50,\\r\\n                            \\\"showWordLimit\\\": false,\\r\\n                            \\\"prefixIcon\\\": \\\"\\\",\\r\\n                            \\\"suffixIcon\\\": \\\"\\\",\\r\\n                            \\\"appendButton\\\": false,\\r\\n                            \\\"appendButtonDisabled\\\": false,\\r\\n                            \\\"buttonIcon\\\": \\\"\\\",\\r\\n                            \\\"iconType\\\": \\\"\\\",\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"onInput\\\": \\\"\\\",\\r\\n                            \\\"onChange\\\": \\\"\\\",\\r\\n                            \\\"onFocus\\\": \\\"\\\",\\r\\n                            \\\"onBlur\\\": \\\"\\\",\\r\\n                            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                            \\\"onValidate\\\": \\\"\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"input55295\\\"\\r\\n                        },\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"input\\\",\\r\\n                          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                          \\\"formItemFlag\\\": true,\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"数量\\\",\\r\\n                            \\\"label\\\": \\\"WMSInventory.MENGE\\\",\\r\\n                            \\\"labelAlign\\\": \\\"label-center-align\\\",\\r\\n                            \\\"type\\\": \\\"text\\\",\\r\\n                            \\\"defaultValue\\\": \\\"\\\",\\r\\n                            \\\"displayStyle\\\": \\\"block\\\",\\r\\n                            \\\"placeholder\\\": \\\"\\\",\\r\\n                            \\\"columnWidth\\\": 110,\\r\\n                            \\\"size\\\": \\\"\\\",\\r\\n                            \\\"labelWidth\\\": null,\\r\\n                            \\\"labelHidden\\\": false,\\r\\n                            \\\"readonly\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"clearable\\\": true,\\r\\n                            \\\"showPassword\\\": false,\\r\\n                            \\\"required\\\": false,\\r\\n                            \\\"requiredHint\\\": \\\"\\\",\\r\\n                            \\\"validation\\\": [\\r\\n                              \\\"noBlankStart\\\",\\r\\n                              \\\"noBlankEnd\\\"\\r\\n                            ],\\r\\n                            \\\"validationHint\\\": \\\"\\\",\\r\\n                            \\\"formatter\\\": \\\"\\\",\\r\\n                            \\\"columnFixed\\\": false,\\r\\n                            \\\"columnFiltering\\\": false,\\r\\n                            \\\"columnSorting\\\": false,\\r\\n                            \\\"columnSortingType\\\": \\\"\\\",\\r\\n                            \\\"customClass\\\": \\\"\\\",\\r\\n                            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                            \\\"labelFontSize\\\": \\\"\\\",\\r\\n                            \\\"labelBold\\\": false,\\r\\n                            \\\"labelItalic\\\": false,\\r\\n                            \\\"labelUnderline\\\": false,\\r\\n                            \\\"labelLineThrough\\\": false,\\r\\n                            \\\"labelIconClass\\\": null,\\r\\n                            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                            \\\"labelTooltip\\\": null,\\r\\n                            \\\"labelIconType\\\": \\\"\\\",\\r\\n                            \\\"minLength\\\": 0,\\r\\n                            \\\"maxLength\\\": 9,\\r\\n                            \\\"showWordLimit\\\": false,\\r\\n                            \\\"prefixIcon\\\": \\\"\\\",\\r\\n                            \\\"suffixIcon\\\": \\\"\\\",\\r\\n                            \\\"appendButton\\\": false,\\r\\n                            \\\"appendButtonDisabled\\\": false,\\r\\n                            \\\"buttonIcon\\\": \\\"\\\",\\r\\n                            \\\"iconType\\\": \\\"\\\",\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"onInput\\\": \\\"\\\",\\r\\n                            \\\"onChange\\\": \\\"\\\",\\r\\n                            \\\"onFocus\\\": \\\"\\\",\\r\\n                            \\\"onBlur\\\": \\\"\\\",\\r\\n                            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                            \\\"onValidate\\\": \\\"\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"input69788\\\"\\r\\n                        },\\r\\n                        {\\r\\n                          \\\"type\\\": \\\"input\\\",\\r\\n                          \\\"icon\\\": \\\"iconoir:input-field\\\",\\r\\n                          \\\"formItemFlag\\\": true,\\r\\n                          \\\"options\\\": {\\r\\n                            \\\"name\\\": \\\"工厂\\\",\\r\\n                            \\\"label\\\": \\\"Factory.Name\\\",\\r\\n                            \\\"labelAlign\\\": \\\"label-center-align\\\",\\r\\n                            \\\"type\\\": \\\"text\\\",\\r\\n                            \\\"defaultValue\\\": \\\"\\\",\\r\\n                            \\\"displayStyle\\\": \\\"block\\\",\\r\\n                            \\\"placeholder\\\": \\\"\\\",\\r\\n                            \\\"columnWidth\\\": 110,\\r\\n                            \\\"size\\\": \\\"\\\",\\r\\n                            \\\"labelWidth\\\": null,\\r\\n                            \\\"labelHidden\\\": false,\\r\\n                            \\\"readonly\\\": false,\\r\\n                            \\\"disabled\\\": false,\\r\\n                            \\\"hidden\\\": false,\\r\\n                            \\\"clearable\\\": true,\\r\\n                            \\\"showPassword\\\": false,\\r\\n                            \\\"required\\\": false,\\r\\n                            \\\"requiredHint\\\": \\\"\\\",\\r\\n                            \\\"validation\\\": [\\r\\n                              \\\"noBlankStart\\\",\\r\\n                              \\\"noBlankEnd\\\"\\r\\n                            ],\\r\\n                            \\\"validationHint\\\": \\\"\\\",\\r\\n                            \\\"formatter\\\": \\\"\\\",\\r\\n                            \\\"columnFixed\\\": false,\\r\\n                            \\\"columnFiltering\\\": false,\\r\\n                            \\\"columnSorting\\\": false,\\r\\n                            \\\"columnSortingType\\\": \\\"\\\",\\r\\n                            \\\"customClass\\\": \\\"\\\",\\r\\n                            \\\"labelFontFamily\\\": \\\"\\\",\\r\\n                            \\\"labelFontSize\\\": \\\"\\\",\\r\\n                            \\\"labelBold\\\": false,\\r\\n                            \\\"labelItalic\\\": false,\\r\\n                            \\\"labelUnderline\\\": false,\\r\\n                            \\\"labelLineThrough\\\": false,\\r\\n                            \\\"labelIconClass\\\": null,\\r\\n                            \\\"labelIconPosition\\\": \\\"rear\\\",\\r\\n                            \\\"labelTooltip\\\": null,\\r\\n                            \\\"labelIconType\\\": \\\"\\\",\\r\\n                            \\\"minLength\\\": 0,\\r\\n                            \\\"maxLength\\\": 30,\\r\\n                            \\\"showWordLimit\\\": false,\\r\\n                            \\\"prefixIcon\\\": \\\"\\\",\\r\\n                            \\\"suffixIcon\\\": \\\"\\\",\\r\\n                            \\\"appendButton\\\": false,\\r\\n                            \\\"appendButtonDisabled\\\": false,\\r\\n                            \\\"buttonIcon\\\": \\\"\\\",\\r\\n                            \\\"iconType\\\": \\\"\\\",\\r\\n                            \\\"onCreated\\\": \\\"\\\",\\r\\n                            \\\"onMounted\\\": \\\"\\\",\\r\\n                            \\\"onInput\\\": \\\"\\\",\\r\\n                            \\\"onChange\\\": \\\"\\\",\\r\\n                            \\\"onFocus\\\": \\\"\\\",\\r\\n                            \\\"onBlur\\\": \\\"\\\",\\r\\n                            \\\"onKeypressEnter\\\": \\\"\\\",\\r\\n                            \\\"onValidate\\\": \\\"\\\"\\r\\n                          },\\r\\n                          \\\"id\\\": \\\"input62628\\\"\\r\\n                        }\\r\\n                      ],\\r\\n                      \\\"options\\\": {\\r\\n                        \\\"name\\\": \\\"VM_INVENTORY_DETAILs\\\",\\r\\n                        \\\"hidden\\\": false,\\r\\n                        \\\"rowSpacing\\\": 8,\\r\\n                        \\\"height\\\": 300,\\r\\n                        \\\"dataSourceUrl\\\": \\\"VM_Inventory_Detail/getPageData\\\",\\r\\n                        \\\"resultPath\\\": \\\"data.rows\\\",\\r\\n                        \\\"disabled\\\": false,\\r\\n                        \\\"readonly\\\": true,\\r\\n                        \\\"loadTreeData\\\": false,\\r\\n                        \\\"rowKey\\\": \\\"__row_key\\\",\\r\\n                        \\\"childrenKey\\\": \\\"children\\\",\\r\\n                        \\\"stripe\\\": true,\\r\\n                        \\\"showIndex\\\": true,\\r\\n                        \\\"showCheckBox\\\": true,\\r\\n                        \\\"paging\\\": true,\\r\\n                        \\\"smallPagination\\\": false,\\r\\n                        \\\"border\\\": true,\\r\\n                        \\\"size\\\": \\\"default\\\",\\r\\n                        \\\"pagination\\\": {\\r\\n                          \\\"currentPage\\\": 1,\\r\\n                          \\\"pageSizes\\\": [\\r\\n                            10,\\r\\n                            15,\\r\\n                            20,\\r\\n                            30,\\r\\n                            50,\\r\\n                            100,\\r\\n                            200\\r\\n                          ],\\r\\n                          \\\"pageSize\\\": 20,\\r\\n                          \\\"total\\\": 366,\\r\\n                          \\\"pagerCount\\\": 5\\r\\n                        },\\r\\n                        \\\"defaultValue\\\": [\\r\\n                          {}\\r\\n                        ],\\r\\n                        \\\"onLoadBefore\\\": \\\"const formData = this.formModel;\\\\r\\\\nif (formData.__optype === 'A') {\\\\r\\\\n  //不执行后续的查询\\\\r\\\\n  return false\\\\r\\\\n} else if (formData.__optype === 'U') {\\\\r\\\\n  const parentRef = this.getFormRef().parentFormRef;\\\\r\\\\n  this.permissionTable = parentRef.tableInfo.tableName;\\\\r\\\\n  param.wheres.push({ name: this.foreignKey, value: formData[\\\\\\\"MATERIAL\\\\\\\"], displayType: '=' })\\\\r\\\\n  param.orderbys = [{ sort: \\\\\\\"排序\\\\\\\", order: \\\\\\\"asc\\\\\\\" }]\\\\r\\\\n}\\\",\\r\\n                        \\\"onPageSizeChange\\\": \\\"\\\",\\r\\n                        \\\"onCurrentPageChange\\\": \\\"\\\",\\r\\n                        \\\"onSelectionChange\\\": \\\"\\\",\\r\\n                        \\\"onHideOperationButton\\\": \\\"\\\",\\r\\n                        \\\"onDisableOperationButton\\\": \\\"\\\",\\r\\n                        \\\"onGetOperationButtonLabel\\\": \\\"\\\",\\r\\n                        \\\"onOperationButtonClick\\\": \\\"\\\",\\r\\n                        \\\"onHeaderClick\\\": \\\"\\\",\\r\\n                        \\\"onRowClick\\\": \\\"\\\",\\r\\n                        \\\"onRowDoubleClick\\\": \\\"\\\",\\r\\n                        \\\"onCellClick\\\": \\\"\\\",\\r\\n                        \\\"onCellDoubleClick\\\": \\\"\\\",\\r\\n                        \\\"onGetRowClassName\\\": \\\"\\\",\\r\\n                        \\\"onGetSpanMethod\\\": \\\"\\\",\\r\\n                        \\\"label\\\": \\\"data-table\\\",\\r\\n                        \\\"onlyChangedData\\\": true,\\r\\n                        \\\"onCreated\\\": \\\"this.foreignKey = '物料';\\\"\\r\\n                      },\\r\\n                      \\\"id\\\": \\\"datatable109857\\\"\\r\\n                    }\\r\\n                  ],\\r\\n                  \\\"options\\\": {\\r\\n                    \\\"name\\\": \\\"tabpane20341\\\",\\r\\n                    \\\"label\\\": \\\"库存明细\\\",\\r\\n                    \\\"disabled\\\": false,\\r\\n                    \\\"lazy\\\": false,\\r\\n                    \\\"closable\\\": false,\\r\\n                    \\\"hidden\\\": false\\r\\n                  },\\r\\n                  \\\"id\\\": \\\"tabpane58712\\\"\\r\\n                }\\r\\n              ],\\r\\n              \\\"options\\\": {\\r\\n                \\\"name\\\": \\\"tabDetail\\\",\\r\\n                \\\"hidden\\\": false,\\r\\n                \\\"tabType\\\": \\\"border-card\\\",\\r\\n                \\\"tabPosition\\\": \\\"top\\\",\\r\\n                \\\"defaultValue\\\": \\\"tabpane_detail\\\"\\r\\n              },\\r\\n              \\\"id\\\": \\\"tab70737\\\"\\r\\n            }\\r\\n          ],\\r\\n          \\\"options\\\": {\\r\\n            \\\"name\\\": \\\"dialogbody65181\\\",\\r\\n            \\\"hidden\\\": false,\\r\\n            \\\"form\\\": {\\r\\n              \\\"size\\\": \\\"\\\",\\r\\n              \\\"labelPosition\\\": \\\"left\\\",\\r\\n              \\\"labelAlign\\\": \\\"label-left-align\\\",\\r\\n              \\\"labelWidth\\\": \\\"auto\\\",\\r\\n              \\\"customClass\\\": []\\r\\n            },\\r\\n            \\\"label\\\": \\\"dialog-body\\\",\\r\\n            \\\"columnHeight\\\": null\\r\\n          },\\r\\n          \\\"id\\\": \\\"dialogbody65181\\\"\\r\\n        }\\r\\n      ],\\r\\n      \\\"options\\\": {\\r\\n        \\\"name\\\": \\\"dialogEditForm\\\",\\r\\n        \\\"label\\\": \\\"dialog\\\",\\r\\n        \\\"center\\\": false,\\r\\n        \\\"showClose\\\": true,\\r\\n        \\\"columnWidth\\\": 50,\\r\\n        \\\"draggable\\\": true,\\r\\n        \\\"top\\\": \\\"15px\\\",\\r\\n        \\\"fullscreen\\\": true,\\r\\n        \\\"onOpenBefore\\\": \\\"\\\",\\r\\n        \\\"onOpenAfter\\\": \\\"this.setTimeout(() => {\\\\r\\\\n  console.log(this.getBodyFormRef());\\\\r\\\\n  this.getBodyFormRef()?.setReadonlyMode(true);\\\\r\\\\n}, 1200);\\\",\\r\\n        \\\"onSubmit\\\": \\\"const data = await this.getValue();\\\\nconst formData = {\\\\n  mainData: data\\\\n}\\\\nconst rootForm = this.getFormRef();\\\\nif (['A', 'U'].includes(data.__optype)) {\\\\n  const { http } = rootForm.commonApi();\\\\n  const res = await http(data.__optype === 'A' ? rootForm.getUrl('add2', true) : data.__optype === 'U' ? rootForm.getUrl('update2', true) : '', 'post', { data: formData });\\\\n  if (!!res?.status) {\\\\n    rootForm.useMessage.success(res.message);\\\\n    //表格的重新加载\\\\n    (this.getWidgetRef(rootForm.tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false);\\\\n  } else {\\\\n    return false;\\\\n  }\\\\n}\\\\n\\\",\\r\\n        \\\"onCloseBefore\\\": \\\"\\\",\\r\\n        \\\"onCloseAfter\\\": \\\"const parentRef= this.getFormRef();\\\\r\\\\nparentRef.setFormData({'dialogEditForm':undefined});\\\",\\r\\n        \\\"destroyOnClose\\\": true\\r\\n      },\\r\\n      \\\"id\\\": \\\"dialog44798\\\"\\r\\n    }\\r\\n  ],\\r\\n  \\\"formConfig\\\": {\\r\\n    \\\"name\\\": \\\"vForm102307\\\",\\r\\n    \\\"modelName\\\": \\\"formData\\\",\\r\\n    \\\"refName\\\": \\\"vForm\\\",\\r\\n    \\\"rulesName\\\": \\\"rules\\\",\\r\\n    \\\"labelWidth\\\": 80,\\r\\n    \\\"labelPosition\\\": \\\"left\\\",\\r\\n    \\\"size\\\": \\\"small\\\",\\r\\n    \\\"labelAlign\\\": \\\"label-left-align\\\",\\r\\n    \\\"cssCode\\\": \\\".itemAlignTop .field-widget-item {\\\\r\\\\n   vertical-align: top;\\\\r\\\\n}\\\\r\\\\n\\\\r\\\\n.linkColorBlack>.el-link {\\\\r\\\\n   --el-link-text-color: var(--el-text-color-primary);\\\\r\\\\n}\\\\r\\\\n\\\\r\\\\n.mb-0{\\\\r\\\\n  margin-bottom: 0 !important;\\\\r\\\\n}\\\",\\r\\n    \\\"customClass\\\": [\\r\\n      \\\"px-15px\\\"\\r\\n    ],\\r\\n    \\\"functions\\\": \\\"\\\",\\r\\n    \\\"layoutType\\\": \\\"PC\\\",\\r\\n    \\\"jsonVersion\\\": 3,\\r\\n    \\\"disabled\\\": false,\\r\\n    \\\"readonly\\\": false,\\r\\n    \\\"entityName\\\": \\\"WMS_Inventory\\\",\\r\\n    \\\"dicNoList\\\": [],\\r\\n    \\\"onFormCreated\\\": \\\"this.tableInfo={\\\\\\\"tableName\\\\\\\":\\\\\\\"WMS_Inventory\\\\\\\",\\\\\\\"columnCNName\\\\\\\":\\\\\\\"库存\\\\\\\",\\\\\\\"tableKey\\\\\\\":\\\\\\\"ID\\\\\\\",\\\\\\\"expressField\\\\\\\":\\\\\\\"MATERIAL\\\\\\\"};\\\\n      this.getUrl=(action,ignoreSuffix)=>'WMS_Inventory/'+action+(!!!ignoreSuffix?'':'');\\\\n      this.onInit();\\\",\\r\\n    \\\"onFormMounted\\\": \\\"this.onInited();\\\",\\r\\n    \\\"onFormDataChange\\\": \\\"\\\",\\r\\n    \\\"labelFontFamily\\\": null,\\r\\n    \\\"labelFontSize\\\": null,\\r\\n    \\\"lazyDicNoList\\\": [],\\r\\n    \\\"optionItemsObject\\\": []\\r\\n  }\\r\\n}\",\"FORMCONFIG\":null,\"FORMFIELDS\":null,\"TABLECONFIG\":null,\"CREATOR\":\"超级管理员\",\"CREATEDATE\":\"2024-07-01 00:00:00\",\"CREATEID\":1,\"MODIFIER\":\"超级管理员\",\"MODIFYDATE\":\"2024-08-05 09:56:09\",\"MODIFYID\":1,\"FORMSTATUS\":0,\"FORMREVISION\":\"WMS_Inventory\",\"__optype\":null}"}|| || 00-8500f53d937c50fc41bb85958a89a6a1-3fd6506230bfffcd-00 ||end
2024-08-05 09:56:09.3966||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-61ecd1b30a2bd753c4e091a816afcabf-c0308fe1826af4b6-00 ||end
2024-08-05 09:56:09.4108||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (14ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM [Sys_FormDesignOptions] AS [s]|| || 00-61ecd1b30a2bd753c4e091a816afcabf-c0308fe1826af4b6-00 ||end
2024-08-05 09:56:09.4286||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (17ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT [s].[FORMID], [s].[CREATEDATE], [s].[CREATEID], [s].[CREATOR], [s].[FORMOPTIONS], [s].[FORMREVISION], [s].[FORMSTATUS], [s].[MODIFIER], [s].[MODIFYDATE], [s].[MODIFYID], [s].[TITLE]
FROM [Sys_FormDesignOptions] AS [s]
ORDER BY [s].[CREATEDATE] DESC
OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY|| || 00-61ecd1b30a2bd753c4e091a816afcabf-c0308fe1826af4b6-00 ||end
