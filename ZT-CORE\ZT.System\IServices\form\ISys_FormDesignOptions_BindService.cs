/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 */
using ZT.Core.BaseProvider;
using ZT.Core.Utilities;
using ZT.Entity.DomainModels;
using ZT.System.DomainModels;

namespace ZT.System.IServices
{
    public partial interface ISys_FormDesignOptions_BindService : IService<Sys_FormDesignOptions_Bind>
    {
        WebResponseContent Save(SaveModel saveDataModel);

        /// <summary>
        /// 获取绑定表单的对象实例
        /// </summary>
        /// <param name="loadData"></param>
        /// <returns></returns>
        WebResponseContent GetBindFormData(PageDataOptions loadData);
        /// <summary>
        /// 根据对象实例获取实例名(如订单号+版本/工艺名+版本)
        /// </summary>
        /// <param name="bindTable"></param>
        /// <returns></returns>
        WebResponseContent GetBindFormObjectData(string bindTable);
        /// <summary>
        /// 根据实例名获取表单信息(表单名+版本号)
        /// </summary>
        /// <param name="bindTableId"></param>
        /// <returns></returns>
        WebResponseContent GetBindFormIdData(string bindTableId);
    }
}
