﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ZT.Core.Utilities;
using ZT.Entity.DomainModels;

namespace ZT.System.IServices
{
    public partial interface ISys_MenuService
    {
        Task<WebResponseContent> GetMenu();
        List<Sys_Menu> GetCurrentMenuList();

        List<Sys_Menu> GetUserMenuList(int[] roleId);

        Task<WebResponseContent> GetCurrentMenuActionList();

        Task<object> GetMenuActionList(int[] roleId);
        Task<WebResponseContent> Save(Sys_Menu menu);

        Task<WebResponseContent> GetTreeItem(int menuId);

        Task<WebResponseContent> DelMenu(int menuId);
    }
}

