/*
 *接口编写处...
*如果接口需要做Action的权限验证，请在Action上使用属性
*如: [ApiActionPermission("SYS_WORKFLOWDESIGNOPTIONS",Enums.ActionPermissionOptions.Search)]
 */
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using ZT.System.IServices;

namespace ZT.System.Controllers
{
    public partial class SYS_WORKFLOWDESIGNOPTIONSController
    {
        private readonly ISYS_WORKFLOWDESIGNOPTIONSService _service;//访问业务代码
        private readonly IHttpContextAccessor _httpContextAccessor;

        [ActivatorUtilitiesConstructor]
        public SYS_WORKFLOWDESIGNOPTIONSController(
            ISYS_WORKFLOWDESIGNOPTIONSService service,
            IHttpContextAccessor httpContextAccessor
        )
        : base(service)
        {
            _service = service;
            _httpContextAccessor = httpContextAccessor;
        }
    }
}
