2024-08-05 09:54:56.7178||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-421fb41c3428cfd8ba5197bd1423c2e3-37bced0a6abd5875-00 ||end
2024-08-05 09:54:56.7178||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (3ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM [Sys_FormDesignOptions] AS [s]
WHERE ([s].[TITLE] = N'Sys_TableInfo') AND ([s].[FORMREVISION] = N'WMS_Inventory')|| || 00-421fb41c3428cfd8ba5197bd1423c2e3-37bced0a6abd5875-00 ||end
2024-08-05 09:54:56.7279||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (6ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT [s].[FORMID], [s].[CREATEDATE], [s].[CREATEID], [s].[CREATOR], [s].[FORMOPTIONS], [s].[FORMREVISION], [s].[FORMSTATUS], [s].[MODIFIER], [s].[MODIFYDATE], [s].[MODIFYID], [s].[TITLE]
FROM [Sys_FormDesignOptions] AS [s]
WHERE ([s].[TITLE] = N'Sys_TableInfo') AND ([s].[FORMREVISION] = N'WMS_Inventory')
ORDER BY [s].[FORMID] DESC
OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY|| || 00-421fb41c3428cfd8ba5197bd1423c2e3-37bced0a6abd5875-00 ||end
2024-08-05 09:54:56.7610||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-421fb41c3428cfd8ba5197bd1423c2e3-37bced0a6abd5875-00 ||end
2024-08-05 09:54:56.7610||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_FormDesignOptionsController.GetPageData (ZT.WebApi)|| || 00-421fb41c3428cfd8ba5197bd1423c2e3-37bced0a6abd5875-00 ||end
2024-08-05 09:54:56.7610||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"page":1,"rows":30,"wheres":"[{\"name\":\"TITLE\",\"value\":\"Sys_TableInfo\",\"displayType\":\"equal\"},{\"name\":\"FORMREVISION\",\"value\":\"WMS_Inventory\",\"displayType\":\"equal\"}]","orderbys":[]}|| || 00-421fb41c3428cfd8ba5197bd1423c2e3-37bced0a6abd5875-00 ||end
2024-08-05 09:54:56.7610||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":"ResponseType.QuerySuccess","data":{"status":true,"msg":null,"total":1,"rows":[{"FORMID":10544,"TITLE":"Sys_TableInfo","DARAGGEOPTIONS":null,"FORMOPTIONS":"{\r\n  \"widgetList\": [\r\n    {\r\n      \"type\": \"grid\",\r\n      \"category\": \"container\",\r\n      \"icon\": \"ep:grid\",\r\n      \"cols\": [\r\n        {\r\n          \"type\": \"grid-col\",\r\n          \"category\": \"container\",\r\n          \"icon\": \"grid-col\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"key\": 39418,\r\n              \"type\": \"link\",\r\n              \"icon\": \"svg-icon:textLink\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"link27855\",\r\n                \"label\": \"库存\",\r\n                \"labelAlign\": \"\",\r\n                \"type\": \"default\",\r\n                \"columnWidth\": 100,\r\n                \"displayStyle\": \"block\",\r\n                \"underline\": true,\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"target\": \"_self\",\r\n                \"href\": \"\",\r\n                \"customClass\": [\r\n                  \"linkColorBlack\"\r\n                ],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"!text-14px\",\r\n                \"labelBold\": true,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"Grid\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"columnFiltering\": false,\r\n                \"columnSorting\": false,\r\n                \"columnSortingType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onChange\": \"\",\r\n                \"onValidate\": \"\",\r\n                \"lineThrough\": false\r\n              },\r\n              \"id\": \"link27855\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"gridcol61024\",\r\n            \"hidden\": false,\r\n            \"span\": 6,\r\n            \"offset\": 0,\r\n            \"push\": 0,\r\n            \"pull\": 0,\r\n            \"responsive\": false,\r\n            \"md\": 12,\r\n            \"sm\": 12,\r\n            \"xs\": 12,\r\n            \"customClass\": []\r\n          },\r\n          \"id\": \"gridcol61024\"\r\n        },\r\n        {\r\n          \"type\": \"grid-col\",\r\n          \"category\": \"container\",\r\n          \"icon\": \"grid-col\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"key\": 48560,\r\n              \"type\": \"input\",\r\n              \"icon\": \"iconoir:input-field\",\r\n              \"formItemFlag\": true,\r\n              \"options\": {\r\n                \"name\": \"searchParam\",\r\n                \"label\": \"input\",\r\n                \"labelAlign\": \"\",\r\n                \"type\": \"text\",\r\n                \"defaultValue\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"placeholder\": \"敲击回车查询\",\r\n                \"columnWidth\": 200,\r\n                \"size\": \"\",\r\n                \"labelWidth\": null,\r\n                \"labelHidden\": true,\r\n                \"readonly\": false,\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"clearable\": true,\r\n                \"showPassword\": false,\r\n                \"required\": false,\r\n                \"requiredHint\": \"\",\r\n                \"validation\": [],\r\n                \"validationHint\": \"\",\r\n                \"columnFiltering\": false,\r\n                \"columnSorting\": false,\r\n                \"columnSortingType\": \"\",\r\n                \"customClass\": [\r\n                  \"mb-0\"\r\n                ],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"minLength\": 0,\r\n                \"maxLength\": 100,\r\n                \"showWordLimit\": false,\r\n                \"prefixIcon\": \"\",\r\n                \"suffixIcon\": \"\",\r\n                \"appendButton\": false,\r\n                \"appendButtonDisabled\": false,\r\n                \"buttonIcon\": \"\",\r\n                \"iconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"if (!this.getFormRef().checkButtonPermiss('Search')) {\\r\\n  this.setHidden(true);\\r\\n} else {\\r\\n  const timer = setInterval(() => {\\r\\n    if (!!this.getFormRef().tableInfo) {\\r\\n      clearInterval(timer);\\r\\n      this.setHidden(!!!this.getFormRef().tableInfo.expressField);\\r\\n      if (!!this.getFormRef().tableInfo.expressField) {\\r\\n        this.setValue(this.getFormRef().currentRoute.query?.queryParam ?? '');\\r\\n      }\\r\\n    }\\r\\n  }, 200)\\r\\n}\\r\\n\\r\\n\",\r\n                \"onInput\": \"\",\r\n                \"onChange\": \"\",\r\n                \"onFocus\": \"\",\r\n                \"onBlur\": \"this.setValue(this.getValue().trim());\",\r\n                \"onValidate\": \"\",\r\n                \"onKeypressEnter\": \"this.setValue(this.getValue().trim());\\r\\n(this.getWidgetRef(this.getFormRef().tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false)\\r\\n\"\r\n              },\r\n              \"id\": \"input103233\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"btnSearch\",\r\n                \"label\": \"common.search\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": null,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"primary\",\r\n                \"text\": false,\r\n                \"plain\": true,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [\r\n                  \"pr-5px\",\r\n                  \"pt-1px\"\r\n                ],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"Search\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('Search'));\",\r\n                \"onClick\": \"//表格的重新加载\\n(this.getWidgetRef(this.getFormRef().tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false)\\n\"\r\n              },\r\n              \"id\": \"button46992\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"btnAdd\",\r\n                \"label\": \"common.add\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": null,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"success\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [\r\n                  \"pr-5px\",\r\n                  \"pt-1px\"\r\n                ],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"CirclePlus\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('Add'));\",\r\n                \"onClick\": \"(this.getWidgetRef('dialogEditForm')).open();\\n(this.getWidgetRef('dialogEditForm')).setValue({ __optype: 'A' });\\n(this.getWidgetRef('dialogEditForm'))['__currentAction']='ADD';\"\r\n              },\r\n              \"id\": \"button106344\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"btnEdit\",\r\n                \"label\": \"common.edit\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": null,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"primary\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [\r\n                  \"pr-5px\",\r\n                  \"pt-1px\"\r\n                ],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"Edit\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('Update'));\",\r\n                \"onClick\": \"const rootForm = this.getFormRef();\\nconst rows = (this.getWidgetRef(rootForm.tableInfo.tableName)).getSelectionRows();\\nif (rows.length !== 1) {\\n  return rootForm.useMessage.error(this.$t('common.selectNoData', [this.$t('common.edit')]));\\n}\\n(this.getWidgetRef('dialogEditForm')).open();\\n(this.getWidgetRef('dialogEditForm')).setValue({ ...rows[0], __optype: 'U' });\\n(this.getWidgetRef('dialogEditForm'))['__currentAction'] = 'EDIT';\"\r\n              },\r\n              \"id\": \"button121732\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"btnDel\",\r\n                \"label\": \"common.delete\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": null,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"danger\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [\r\n                  \"pr-5px\",\r\n                  \"pt-1px\"\r\n                ],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"Delete\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('Delete'));\",\r\n                \"onClick\": \"const rootForm = this.getFormRef();\\nconst rows = (rootForm.getWidgetRef(rootForm.tableInfo.tableName)).getSelectionRows();\\nif (!rows || rows.length < 1) {\\n  return rootForm.useMessage.error(this.$t('common.selectNoData', [this.$t('common.delete')]));\\n}\\nthis.$confirm(this.$t('common.delMessage'), this.$t('common.delWarning'), {\\n  confirmButtonText: this.$t('common.delOk'),\\n  cancelButtonText: this.$t('common.delCancel'),\\n  type: 'warning',\\n  center: true\\n}).then(() => {\\n  let delKeys = rows.map(x => x[rootForm.tableInfo.tableKey]);\\n  const { http } = rootForm.commonApi();\\n  http(rootForm.getUrl('Del2', true), 'post', { data: delKeys, permissionTable: rootForm.tableInfo.tableName }).then((x) => {\\n    if (!!x?.status) {\\n      rootForm.useMessage.success(x.message);\\n      //表格的重新加载\\n      (rootForm.getWidgetRef(rootForm.tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false);\\n    }\\n  })\\n});\\n\\n\\n\"\r\n              },\r\n              \"id\": \"button20509\"\r\n            },\r\n            {\r\n              \"key\": 15003,\r\n              \"type\": \"dropdown\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"radix-icons:dropdown-menu\",\r\n              \"widgetList\": [\r\n                {\r\n                  \"type\": \"dropdown-item\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 69467,\r\n                      \"type\": \"embedded-form\",\r\n                      \"icon\": \"svg-icon:embedded-form-field\",\r\n                      \"category\": \"container\",\r\n                      \"widgetList\": [],\r\n                      \"options\": {\r\n                        \"name\": \"embeddedform82044\",\r\n                        \"hidden\": false,\r\n                        \"border\": false,\r\n                        \"disabled\": false,\r\n                        \"formOptionParams\": [\r\n                          {\r\n                            \"key\": \"formId\",\r\n                            \"value\": 10141\r\n                          }\r\n                        ],\r\n                        \"form\": {\r\n                          \"customClass\": [\r\n                            \"p-0\"\r\n                          ]\r\n                        },\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"label\": \"embedded-form\",\r\n                        \"customClass\": []\r\n                      },\r\n                      \"id\": \"embeddedform82044\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"dropdownitem29169\",\r\n                    \"label\": \"action 1\",\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('Copy'));\",\r\n                    \"onClick\": \"\",\r\n                    \"columnWidth\": null,\r\n                    \"customClass\": [\r\n                      \"!px-0\"\r\n                    ]\r\n                  },\r\n                  \"id\": \"dropdownitem29169\"\r\n                },\r\n                {\r\n                  \"type\": \"dropdown-item\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 79765,\r\n                      \"type\": \"embedded-form\",\r\n                      \"icon\": \"svg-icon:embedded-form-field\",\r\n                      \"category\": \"container\",\r\n                      \"widgetList\": [],\r\n                      \"options\": {\r\n                        \"name\": \"embeddedform68183\",\r\n                        \"hidden\": false,\r\n                        \"border\": false,\r\n                        \"disabled\": false,\r\n                        \"formOptionParams\": [\r\n                          {\r\n                            \"key\": \"formId\",\r\n                            \"value\": 10240\r\n                          }\r\n                        ],\r\n                        \"form\": {\r\n                          \"customClass\": [\r\n                            \"p-0\"\r\n                          ]\r\n                        },\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"label\": \"embedded-form\"\r\n                      },\r\n                      \"id\": \"embeddedform68183\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"dropdownitem100911\",\r\n                    \"label\": \"action 2\",\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('Freeze'));\",\r\n                    \"onClick\": \"\",\r\n                    \"columnWidth\": null,\r\n                    \"customClass\": [\r\n                      \"!px-0\"\r\n                    ]\r\n                  },\r\n                  \"id\": \"dropdownitem100911\"\r\n                },\r\n                {\r\n                  \"type\": \"dropdown-item\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 96151,\r\n                      \"type\": \"embedded-form\",\r\n                      \"icon\": \"svg-icon:embedded-form-field\",\r\n                      \"category\": \"container\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"type\": \"embedded-form-slot\",\r\n                          \"icon\": \"embedded-form-slot\",\r\n                          \"category\": \"container\",\r\n                          \"draginable\": \"^.+$\",\r\n                          \"internal\": true,\r\n                          \"widgetList\": [],\r\n                          \"options\": {\r\n                            \"name\": \"slotFormPreview\",\r\n                            \"hidden\": false,\r\n                            \"border\": false,\r\n                            \"disabled\": false,\r\n                            \"designable\": false,\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"label\": \"embedded-form-slot\"\r\n                          },\r\n                          \"id\": \"embeddedformslot104952\"\r\n                        }\r\n                      ],\r\n                      \"options\": {\r\n                        \"name\": \"embeddedform61663\",\r\n                        \"hidden\": false,\r\n                        \"border\": false,\r\n                        \"disabled\": false,\r\n                        \"formOptionParams\": [\r\n                          {\r\n                            \"key\": \"formId\",\r\n                            \"value\": 10241\r\n                          }\r\n                        ],\r\n                        \"form\": {\r\n                          \"customClass\": [\r\n                            \"p-0\"\r\n                          ]\r\n                        },\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"label\": \"embedded-form\"\r\n                      },\r\n                      \"id\": \"embeddedform61663\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"dropdownitem33398\",\r\n                    \"label\": \"action 3\",\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('BindForm'));\",\r\n                    \"onClick\": \"\",\r\n                    \"columnWidth\": null,\r\n                    \"customClass\": [\r\n                      \"!px-0\"\r\n                    ]\r\n                  },\r\n                  \"id\": \"dropdownitem33398\"\r\n                },\r\n                {\r\n                  \"type\": \"dropdown-item\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 32789,\r\n                      \"type\": \"embedded-form\",\r\n                      \"icon\": \"svg-icon:embedded-form-field\",\r\n                      \"category\": \"container\",\r\n                      \"widgetList\": [],\r\n                      \"options\": {\r\n                        \"name\": \"embeddedform57613\",\r\n                        \"hidden\": false,\r\n                        \"border\": false,\r\n                        \"disabled\": false,\r\n                        \"formOptionParams\": [\r\n                          {\r\n                            \"key\": \"formId\",\r\n                            \"value\": 10244\r\n                          }\r\n                        ],\r\n                        \"form\": {\r\n                          \"customClass\": [\r\n                            \"p-0\"\r\n                          ]\r\n                        },\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"label\": \"embedded-form\"\r\n                      },\r\n                      \"id\": \"embeddedform57613\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"dropdownitem22281\",\r\n                    \"label\": \"action 7\",\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('historicalTracing'));\",\r\n                    \"onClick\": \"\",\r\n                    \"columnWidth\": null,\r\n                    \"customClass\": [\r\n                      \"!px-0\"\r\n                    ]\r\n                  },\r\n                  \"id\": \"dropdownitem22281\"\r\n                },\r\n                {\r\n                  \"type\": \"dropdown-item\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 97916,\r\n                      \"type\": \"button\",\r\n                      \"icon\": \"svg-icon:button\",\r\n                      \"formItemFlag\": false,\r\n                      \"options\": {\r\n                        \"name\": \"button62675\",\r\n                        \"label\": \"common.exportPage\",\r\n                        \"labelAlign\": \"\",\r\n                        \"columnWidth\": null,\r\n                        \"size\": \"small\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"type\": \"success\",\r\n                        \"text\": true,\r\n                        \"plain\": false,\r\n                        \"round\": false,\r\n                        \"circle\": false,\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": \"Download\",\r\n                        \"labelIconPosition\": \"front\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"pl\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onClick\": \"const rootForm = this.getFormRef().parentFormRef;\\r\\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\\r\\n$table.localExport();\"\r\n                      },\r\n                      \"id\": \"button62675\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"dropdownitem86037\",\r\n                    \"label\": \"common.exportPage\",\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('Export'));\",\r\n                    \"onClick\": \"\",\r\n                    \"columnWidth\": null,\r\n                    \"customClass\": [\r\n                      \"!px-0\"\r\n                    ]\r\n                  },\r\n                  \"id\": \"dropdownitem86037\"\r\n                },\r\n                {\r\n                  \"type\": \"dropdown-item\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 97916,\r\n                      \"type\": \"button\",\r\n                      \"icon\": \"svg-icon:button\",\r\n                      \"formItemFlag\": false,\r\n                      \"options\": {\r\n                        \"name\": \"button80999\",\r\n                        \"label\": \"common.exportAll\",\r\n                        \"labelAlign\": \"\",\r\n                        \"columnWidth\": null,\r\n                        \"size\": \"small\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"type\": \"success\",\r\n                        \"text\": true,\r\n                        \"plain\": false,\r\n                        \"round\": false,\r\n                        \"circle\": false,\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": \"cloud-download\",\r\n                        \"labelIconPosition\": \"front\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"sv\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onClick\": \"const rootForm = this.getFormRef().parentFormRef;\\r\\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\\r\\nconst exportUrl = rootForm.getUrl('export');\\r\\nconst downloadUrl = rootForm.getUrl('downloadFile', true);\\r\\n$table.remoteExport({ exportUrl, downloadUrl });\"\r\n                      },\r\n                      \"id\": \"button80999\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"dropdownitem96091\",\r\n                    \"label\": \"common.exportAll\",\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('RemoteExport'));\",\r\n                    \"onClick\": \"\",\r\n                    \"columnWidth\": null,\r\n                    \"customClass\": [\r\n                      \"!px-0\"\r\n                    ]\r\n                  },\r\n                  \"id\": \"dropdownitem96091\"\r\n                },\r\n                {\r\n                  \"type\": \"dropdown-item\",\r\n                  \"category\": \"container\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-static-(?:button|link|static-text))|(container-outside-embedded-form)$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 97916,\r\n                      \"type\": \"button\",\r\n                      \"icon\": \"svg-icon:button\",\r\n                      \"formItemFlag\": false,\r\n                      \"options\": {\r\n                        \"name\": \"button58159\",\r\n                        \"label\": \"common.adjustColumns\",\r\n                        \"labelAlign\": \"\",\r\n                        \"columnWidth\": null,\r\n                        \"size\": \"small\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"type\": \"primary\",\r\n                        \"text\": true,\r\n                        \"plain\": false,\r\n                        \"round\": false,\r\n                        \"circle\": false,\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": \"Setting\",\r\n                        \"labelIconPosition\": \"front\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"pl\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onClick\": \"const rootForm = this.getFormRef().parentFormRef;\\r\\nconst $table = rootForm.getWidgetRef(rootForm.tableInfo.tableName);\\r\\n$table.openColumnDrawer();\"\r\n                      },\r\n                      \"id\": \"button58159\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"dropdownitem45249\",\r\n                    \"label\": \"common.adjustColumns\",\r\n                    \"disabled\": false,\r\n                    \"hidden\": false,\r\n                    \"labelFontFamily\": \"\",\r\n                    \"labelFontSize\": \"\",\r\n                    \"labelBold\": false,\r\n                    \"labelItalic\": false,\r\n                    \"labelUnderline\": false,\r\n                    \"labelLineThrough\": false,\r\n                    \"labelIconClass\": null,\r\n                    \"labelIconPosition\": \"rear\",\r\n                    \"labelTooltip\": null,\r\n                    \"labelIconType\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"this.setHidden(!this.getFormRef().checkButtonPermiss('AdjustCol'));\",\r\n                    \"onClick\": \"\",\r\n                    \"columnWidth\": null,\r\n                    \"customClass\": [\r\n                      \"!px-0\"\r\n                    ]\r\n                  },\r\n                  \"id\": \"dropdownitem45249\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"dropdown71045\",\r\n                \"label\": \"common.more\",\r\n                \"defaultValue\": \"\",\r\n                \"columnWidth\": null,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"success\",\r\n                \"text\": false,\r\n                \"plain\": true,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [\r\n                  \"pt-1px\"\r\n                ],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onCommand\": \"\"\r\n              },\r\n              \"id\": \"dropdown71045\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"gridcol80749\",\r\n            \"hidden\": false,\r\n            \"span\": 18,\r\n            \"offset\": 0,\r\n            \"push\": 0,\r\n            \"pull\": 0,\r\n            \"responsive\": false,\r\n            \"md\": 12,\r\n            \"sm\": 12,\r\n            \"xs\": 12,\r\n            \"customClass\": [\r\n              \"flex\",\r\n              \"justify-end\",\r\n              \"itemAlignTop\",\r\n              \"pt-1px\",\r\n              \"h-27px\"\r\n            ]\r\n          },\r\n          \"id\": \"gridcol80749\"\r\n        }\r\n      ],\r\n      \"options\": {\r\n        \"name\": \"gridTitle\",\r\n        \"hidden\": false,\r\n        \"gutter\": 0,\r\n        \"colHeight\": null,\r\n        \"customClass\": [\r\n          \"py-8px\"\r\n        ]\r\n      },\r\n      \"id\": \"grid17420\"\r\n    },\r\n    {\r\n      \"key\": 27644,\r\n      \"type\": \"data-table\",\r\n      \"category\": \"container\",\r\n      \"icon\": \"svg-icon:data-table\",\r\n      \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n      \"widgetList\": [\r\n        {\r\n          \"type\": \"link\",\r\n          \"icon\": \"svg-icon:textLink\",\r\n          \"formItemFlag\": false,\r\n          \"options\": {\r\n            \"name\": \"MATERIAL\",\r\n            \"label\": \"WMSInventory.MATERIAL\",\r\n            \"labelAlign\": \"\",\r\n            \"type\": \"primary\",\r\n            \"columnWidth\": 110,\r\n            \"displayStyle\": \"block\",\r\n            \"underline\": true,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"target\": \"_self\",\r\n            \"href\": \"\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"columnFixed\": \"left\",\r\n            \"columnFiltering\": true,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"\",\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onValidate\": \"\",\r\n            \"onClick\": \"(this.getWidgetRef('dialogEditForm')).open();\\r\\nconst rootForm = this.getFormRef();\\r\\nconst row = (this.getWidgetRef(rootForm.tableInfo.tableName)).getValue(false)[this.subFormRowIndex];\\r\\n(this.getWidgetRef('dialogEditForm')).setValue({ ...row, __optype: 'U' });\\r\\n(this.getWidgetRef('dialogEditForm'))['__currentAction'] = 'EDIT';\"\r\n          },\r\n          \"id\": \"link24523\"\r\n        },\r\n        {\r\n          \"type\": \"textarea\",\r\n          \"icon\": \"bi:textarea-resize\",\r\n          \"formItemFlag\": true,\r\n          \"options\": {\r\n            \"name\": \"MATERIAL_DESCRIPTION\",\r\n            \"label\": \"WMSInventory.MATERIAL_DESCRIPTION\",\r\n            \"labelAlign\": \"\",\r\n            \"rows\": 3,\r\n            \"defaultValue\": \"\",\r\n            \"displayStyle\": \"block\",\r\n            \"placeholder\": \"\",\r\n            \"columnWidth\": 220,\r\n            \"size\": \"\",\r\n            \"labelWidth\": null,\r\n            \"labelHidden\": false,\r\n            \"readonly\": false,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"required\": false,\r\n            \"requiredHint\": \"\",\r\n            \"validation\": \"\",\r\n            \"validationHint\": \"\",\r\n            \"formatter\": \"\",\r\n            \"columnFixed\": false,\r\n            \"columnFiltering\": false,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"zhCn\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"minLength\": 0,\r\n            \"maxLength\": 100,\r\n            \"showWordLimit\": true,\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onInput\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onFocus\": \"\",\r\n            \"onBlur\": \"\",\r\n            \"onValidate\": \"\"\r\n          },\r\n          \"id\": \"textarea97487\"\r\n        },\r\n        {\r\n          \"type\": \"number\",\r\n          \"icon\": \"svg-icon:number-field\",\r\n          \"formItemFlag\": true,\r\n          \"options\": {\r\n            \"name\": \"MENGE\",\r\n            \"label\": \"WMSInventory.MENGE\",\r\n            \"labelAlign\": \"\",\r\n            \"defaultValue\": null,\r\n            \"displayStyle\": \"block\",\r\n            \"placeholder\": \"\",\r\n            \"columnWidth\": 110,\r\n            \"size\": \"\",\r\n            \"labelWidth\": null,\r\n            \"labelHidden\": false,\r\n            \"readonly\": false,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"required\": true,\r\n            \"requiredHint\": \"\",\r\n            \"columnFixed\": false,\r\n            \"columnFiltering\": false,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"min\": -10000000000,\r\n            \"max\": 100000000000,\r\n            \"precision\": 2,\r\n            \"step\": 1,\r\n            \"controlsPosition\": \"\",\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onFocus\": \"\",\r\n            \"onBlur\": \"\",\r\n            \"onValidate\": \"\"\r\n          },\r\n          \"id\": \"number39622\"\r\n        },\r\n        {\r\n          \"type\": \"input\",\r\n          \"icon\": \"iconoir:input-field\",\r\n          \"formItemFlag\": true,\r\n          \"options\": {\r\n            \"name\": \"UNIT\",\r\n            \"label\": \"WMSInventory.UNIT\",\r\n            \"labelAlign\": \"\",\r\n            \"type\": \"text\",\r\n            \"defaultValue\": \"\",\r\n            \"displayStyle\": \"block\",\r\n            \"placeholder\": \"\",\r\n            \"columnWidth\": 110,\r\n            \"size\": \"\",\r\n            \"labelWidth\": null,\r\n            \"labelHidden\": false,\r\n            \"readonly\": false,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"clearable\": true,\r\n            \"showPassword\": false,\r\n            \"required\": false,\r\n            \"requiredHint\": \"\",\r\n            \"validation\": [\r\n              \"noBlankStart\",\r\n              \"noBlankEnd\"\r\n            ],\r\n            \"validationHint\": \"\",\r\n            \"formatter\": \"\",\r\n            \"columnFixed\": false,\r\n            \"columnFiltering\": false,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"zhCn\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"minLength\": 0,\r\n            \"maxLength\": 6,\r\n            \"showWordLimit\": true,\r\n            \"prefixIcon\": \"\",\r\n            \"suffixIcon\": \"\",\r\n            \"appendButton\": false,\r\n            \"appendButtonDisabled\": false,\r\n            \"buttonIcon\": \"\",\r\n            \"iconType\": \"\",\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onInput\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onFocus\": \"\",\r\n            \"onBlur\": \"\",\r\n            \"onKeypressEnter\": \"\",\r\n            \"onValidate\": \"\"\r\n          },\r\n          \"id\": \"input75077\"\r\n        },\r\n        {\r\n          \"type\": \"number\",\r\n          \"icon\": \"svg-icon:number-field\",\r\n          \"formItemFlag\": true,\r\n          \"options\": {\r\n            \"name\": \"ONLINE_QTY\",\r\n            \"label\": \"WMSInventory.ONLINE_QTY\",\r\n            \"labelAlign\": \"\",\r\n            \"defaultValue\": null,\r\n            \"displayStyle\": \"block\",\r\n            \"placeholder\": \"\",\r\n            \"columnWidth\": 110,\r\n            \"size\": \"\",\r\n            \"labelWidth\": null,\r\n            \"labelHidden\": false,\r\n            \"readonly\": true,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"required\": false,\r\n            \"requiredHint\": \"\",\r\n            \"columnFixed\": false,\r\n            \"columnFiltering\": false,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"min\": -10000000000,\r\n            \"max\": 100000000000,\r\n            \"precision\": 2,\r\n            \"step\": 1,\r\n            \"controlsPosition\": \"\",\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onFocus\": \"\",\r\n            \"onBlur\": \"\",\r\n            \"onValidate\": \"\"\r\n          },\r\n          \"id\": \"number34132\"\r\n        },\r\n        {\r\n          \"type\": \"select\",\r\n          \"icon\": \"svg-icon:select-field\",\r\n          \"formItemFlag\": true,\r\n          \"options\": {\r\n            \"name\": \"FILTERTAGS\",\r\n            \"label\": \"FilterTag.FilterTag\",\r\n            \"labelAlign\": \"\",\r\n            \"defaultValue\": \"\",\r\n            \"displayStyle\": \"block\",\r\n            \"placeholder\": \"\",\r\n            \"columnWidth\": 110,\r\n            \"size\": \"\",\r\n            \"labelWidth\": null,\r\n            \"labelHidden\": false,\r\n            \"readonly\": false,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"clearable\": true,\r\n            \"filterable\": true,\r\n            \"allowCreate\": false,\r\n            \"remote\": false,\r\n            \"multiple\": false,\r\n            \"multipleLimit\": 0,\r\n            \"lazy\": false,\r\n            \"optionItems\": [\r\n              {\r\n                \"label\": \"select 1\",\r\n                \"value\": \"1\"\r\n              },\r\n              {\r\n                \"label\": \"select 2\",\r\n                \"value\": \"2\"\r\n              },\r\n              {\r\n                \"label\": \"select 3\",\r\n                \"value\": \"3\"\r\n              }\r\n            ],\r\n            \"required\": false,\r\n            \"requiredHint\": \"\",\r\n            \"dataSource\": \"FILTERTAG\",\r\n            \"optionTagName\": \"key\",\r\n            \"optionValueName\": \"value\",\r\n            \"trigger\": null,\r\n            \"formatter\": \"\",\r\n            \"columnFixed\": false,\r\n            \"columnFiltering\": false,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onRemoteQuery\": \"\",\r\n            \"onRemoteQueryBefore\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onFocus\": \"\",\r\n            \"onBlur\": \"\",\r\n            \"onValidate\": \"\"\r\n          },\r\n          \"id\": \"select39495\"\r\n        },\r\n        {\r\n          \"type\": \"input\",\r\n          \"icon\": \"iconoir:input-field\",\r\n          \"formItemFlag\": true,\r\n          \"options\": {\r\n            \"name\": \"MO_LOT_NUMBER\",\r\n            \"label\": \"WMSPickingDetail.MO_LOT_NUMBER\",\r\n            \"labelAlign\": \"\",\r\n            \"type\": \"text\",\r\n            \"defaultValue\": \"\",\r\n            \"displayStyle\": \"block\",\r\n            \"placeholder\": \"\",\r\n            \"columnWidth\": 110,\r\n            \"size\": \"\",\r\n            \"labelWidth\": null,\r\n            \"labelHidden\": false,\r\n            \"readonly\": false,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"clearable\": true,\r\n            \"showPassword\": false,\r\n            \"required\": false,\r\n            \"requiredHint\": \"\",\r\n            \"validation\": [\r\n              \"noBlankStart\",\r\n              \"noBlankEnd\"\r\n            ],\r\n            \"validationHint\": \"\",\r\n            \"formatter\": \"\",\r\n            \"columnFixed\": false,\r\n            \"columnFiltering\": false,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"minLength\": 0,\r\n            \"maxLength\": 50,\r\n            \"showWordLimit\": true,\r\n            \"prefixIcon\": \"\",\r\n            \"suffixIcon\": \"\",\r\n            \"appendButton\": false,\r\n            \"appendButtonDisabled\": false,\r\n            \"buttonIcon\": \"\",\r\n            \"iconType\": \"\",\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onInput\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onFocus\": \"\",\r\n            \"onBlur\": \"\",\r\n            \"onKeypressEnter\": \"\",\r\n            \"onValidate\": \"\"\r\n          },\r\n          \"id\": \"input59705\"\r\n        },\r\n        {\r\n          \"type\": \"input\",\r\n          \"icon\": \"iconoir:input-field\",\r\n          \"formItemFlag\": true,\r\n          \"options\": {\r\n            \"name\": \"SO_LOT_NUMBER\",\r\n            \"label\": \"WMSPickingDetail.SO_LOT_NUMBER\",\r\n            \"labelAlign\": \"\",\r\n            \"type\": \"text\",\r\n            \"defaultValue\": \"\",\r\n            \"displayStyle\": \"block\",\r\n            \"placeholder\": \"\",\r\n            \"columnWidth\": 220,\r\n            \"size\": \"\",\r\n            \"labelWidth\": null,\r\n            \"labelHidden\": false,\r\n            \"readonly\": false,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"clearable\": true,\r\n            \"showPassword\": false,\r\n            \"required\": false,\r\n            \"requiredHint\": \"\",\r\n            \"validation\": [\r\n              \"noBlankStart\",\r\n              \"noBlankEnd\"\r\n            ],\r\n            \"validationHint\": \"\",\r\n            \"formatter\": \"\",\r\n            \"columnFixed\": false,\r\n            \"columnFiltering\": false,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"minLength\": 0,\r\n            \"maxLength\": 300,\r\n            \"showWordLimit\": true,\r\n            \"prefixIcon\": \"\",\r\n            \"suffixIcon\": \"\",\r\n            \"appendButton\": false,\r\n            \"appendButtonDisabled\": false,\r\n            \"buttonIcon\": \"\",\r\n            \"iconType\": \"\",\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onInput\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onFocus\": \"\",\r\n            \"onBlur\": \"\",\r\n            \"onKeypressEnter\": \"\",\r\n            \"onValidate\": \"\"\r\n          },\r\n          \"id\": \"input97805\"\r\n        },\r\n        {\r\n          \"type\": \"input\",\r\n          \"icon\": \"iconoir:input-field\",\r\n          \"formItemFlag\": true,\r\n          \"options\": {\r\n            \"name\": \"SUPPLIER_NAME\",\r\n            \"label\": \"WMSPickingDetail.SUPPLIER_NAME\",\r\n            \"labelAlign\": \"\",\r\n            \"type\": \"text\",\r\n            \"defaultValue\": \"\",\r\n            \"displayStyle\": \"block\",\r\n            \"placeholder\": \"\",\r\n            \"columnWidth\": 120,\r\n            \"size\": \"\",\r\n            \"labelWidth\": null,\r\n            \"labelHidden\": false,\r\n            \"readonly\": false,\r\n            \"disabled\": false,\r\n            \"hidden\": false,\r\n            \"clearable\": true,\r\n            \"showPassword\": false,\r\n            \"required\": false,\r\n            \"requiredHint\": \"\",\r\n            \"validation\": [\r\n              \"noBlankStart\",\r\n              \"noBlankEnd\"\r\n            ],\r\n            \"validationHint\": \"\",\r\n            \"formatter\": \"\",\r\n            \"columnFixed\": false,\r\n            \"columnFiltering\": false,\r\n            \"columnSorting\": false,\r\n            \"columnSortingType\": \"\",\r\n            \"customClass\": \"\",\r\n            \"labelFontFamily\": \"\",\r\n            \"labelFontSize\": \"\",\r\n            \"labelBold\": false,\r\n            \"labelItalic\": false,\r\n            \"labelUnderline\": false,\r\n            \"labelLineThrough\": false,\r\n            \"labelIconClass\": null,\r\n            \"labelIconPosition\": \"rear\",\r\n            \"labelTooltip\": null,\r\n            \"labelIconType\": \"\",\r\n            \"minLength\": 0,\r\n            \"maxLength\": 100,\r\n            \"showWordLimit\": true,\r\n            \"prefixIcon\": \"\",\r\n            \"suffixIcon\": \"\",\r\n            \"appendButton\": false,\r\n            \"appendButtonDisabled\": false,\r\n            \"buttonIcon\": \"\",\r\n            \"iconType\": \"\",\r\n            \"onCreated\": \"\",\r\n            \"onMounted\": \"\",\r\n            \"onInput\": \"\",\r\n            \"onChange\": \"\",\r\n            \"onFocus\": \"\",\r\n            \"onBlur\": \"\",\r\n            \"onKeypressEnter\": \"\",\r\n            \"onValidate\": \"\"\r\n          },\r\n          \"id\": \"input82624\"\r\n        }\r\n      ],\r\n      \"options\": {\r\n        \"name\": \"WMS_Inventory\",\r\n        \"hidden\": false,\r\n        \"rowSpacing\": 8,\r\n        \"height\": \"calc(100vh - var(--tags-view-height) - var(--top-tool-height) - var(--app-footer-height) - 48px - 52px - 1px)\",\r\n        \"dataSourceUrl\": \"WMS_Inventory/getPageData\",\r\n        \"resultPath\": \"data.rows\",\r\n        \"disabled\": false,\r\n        \"readonly\": true,\r\n        \"loadTreeData\": false,\r\n        \"rowKey\": \"__row_key\",\r\n        \"childrenKey\": \"children\",\r\n        \"stripe\": true,\r\n        \"showIndex\": false,\r\n        \"showCheckBox\": true,\r\n        \"paging\": true,\r\n        \"smallPagination\": false,\r\n        \"border\": true,\r\n        \"size\": \"default\",\r\n        \"pagination\": {\r\n          \"currentPage\": 1,\r\n          \"pageSizes\": [\r\n            10,\r\n            15,\r\n            20,\r\n            30,\r\n            50,\r\n            100,\r\n            200\r\n          ],\r\n          \"pageSize\": 20,\r\n          \"total\": 366,\r\n          \"pagerCount\": 5\r\n        },\r\n        \"defaultValue\": [\r\n          {}\r\n        ],\r\n        \"onLoadBefore\": \"const searchParamVal = (this.getWidgetRef('searchParam'))?.getValue();\\r\\nif (!!searchParamVal) {\\r\\n  param.wheres.push({ name: this.getFormRef().tableInfo.expressField, value: searchParamVal, displayType: 'like' });\\r\\n}\",\r\n        \"onPageSizeChange\": \"\",\r\n        \"onCurrentPageChange\": \"\",\r\n        \"onSelectionChange\": \"\",\r\n        \"onHideOperationButton\": \"\",\r\n        \"onDisableOperationButton\": \"\",\r\n        \"onGetOperationButtonLabel\": \"\",\r\n        \"onOperationButtonClick\": \"\",\r\n        \"onHeaderClick\": \"\",\r\n        \"onRowClick\": \"\",\r\n        \"onRowDoubleClick\": \"\",\r\n        \"onCellClick\": \"\",\r\n        \"onCellDoubleClick\": \"\",\r\n        \"onGetRowClassName\": \"\",\r\n        \"onGetSpanMethod\": \"\",\r\n        \"label\": \"库存\",\r\n        \"showSummary\": true\r\n      },\r\n      \"id\": \"datatable68018\"\r\n    },\r\n    {\r\n      \"type\": \"dialog\",\r\n      \"category\": \"container\",\r\n      \"icon\": \"svg-icon:dialog\",\r\n      \"widgetList\": [\r\n        {\r\n          \"type\": \"dialog-header\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"static-text\",\r\n              \"icon\": \"svg-icon:static-text\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"statictext63806\",\r\n                \"label\": \"static-text\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 100,\r\n                \"hidden\": false,\r\n                \"textContent\": \"编辑\",\r\n                \"displayStyle\": \"block\",\r\n                \"whiteSpace\": \"normal\",\r\n                \"columnFiltering\": false,\r\n                \"columnSorting\": false,\r\n                \"columnSortingType\": \"\",\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"MicrosoftYahei\",\r\n                \"labelFontSize\": \"!text-18.7px\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": \"InfoFilled\",\r\n                \"labelIconPosition\": \"front\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"pl\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"const parentRef = this.getFormRef().parentFormRef;\\r\\nconst action = parentRef.getWidgetRef('dialogEditForm')['__currentAction'];\\r\\nthis.field.options.textContent = this.$t(parentRef.tableInfo.columnCNName) + '--' + this.$t('common.look');\"\r\n              },\r\n              \"id\": \"statictext63806\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogheader46762\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": []\r\n            },\r\n            \"label\": \"dialog-header\"\r\n          },\r\n          \"id\": \"dialogheader46762\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-footer\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button21984\",\r\n                \"label\": \"common.cancel\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"info\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialogEditForm')?.handleClose();\"\r\n              },\r\n              \"id\": \"button21984\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"btnReset\",\r\n                \"label\": \"common.reset\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": false,\r\n                \"type\": \"warning\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"//获取表单数据\\r\\nconst parentRef = this.getFormRef().parentFormRef;\\r\\nsetTimeout(async () => {\\r\\n  this._formdata = await parentRef.getWidgetRef('dialogEditForm')?.getValue(false);\\r\\n}, 1000)\\r\\n\\r\\n\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialogEditForm')?.refresh(this._formdata);\"\r\n              },\r\n              \"id\": \"button84251\"\r\n            },\r\n            {\r\n              \"type\": \"button\",\r\n              \"icon\": \"svg-icon:button\",\r\n              \"formItemFlag\": false,\r\n              \"options\": {\r\n                \"name\": \"button34967\",\r\n                \"label\": \"common.submit\",\r\n                \"labelAlign\": \"\",\r\n                \"columnWidth\": 75,\r\n                \"size\": \"\",\r\n                \"displayStyle\": \"inline-flex\",\r\n                \"disabled\": false,\r\n                \"hidden\": true,\r\n                \"type\": \"primary\",\r\n                \"text\": false,\r\n                \"plain\": false,\r\n                \"round\": false,\r\n                \"circle\": false,\r\n                \"customClass\": [],\r\n                \"labelFontFamily\": \"\",\r\n                \"labelFontSize\": \"\",\r\n                \"labelBold\": false,\r\n                \"labelItalic\": false,\r\n                \"labelUnderline\": false,\r\n                \"labelLineThrough\": false,\r\n                \"labelIconClass\": null,\r\n                \"labelIconPosition\": \"rear\",\r\n                \"labelTooltip\": null,\r\n                \"labelIconType\": \"\",\r\n                \"onCreated\": \"\",\r\n                \"onMounted\": \"\",\r\n                \"onClick\": \"const parentRef= this.getFormRef().parentFormRef;\\r\\nparentRef.getWidgetRef('dialogEditForm')?.submit();\"\r\n              },\r\n              \"id\": \"button34967\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogfooter31118\",\r\n            \"columnWidth\": 80,\r\n            \"columnHeight\": 35,\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"small\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": 80,\r\n              \"customClass\": [\r\n                \"flex\",\r\n                \"justify-end\"\r\n              ]\r\n            },\r\n            \"label\": \"dialog-footer\"\r\n          },\r\n          \"id\": \"dialogfooter31118\"\r\n        },\r\n        {\r\n          \"type\": \"dialog-body\",\r\n          \"category\": \"container\",\r\n          \"internal\": true,\r\n          \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n          \"widgetList\": [\r\n            {\r\n              \"key\": 92283,\r\n              \"type\": \"grid\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"ep:grid\",\r\n              \"cols\": [\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"input\",\r\n                      \"icon\": \"iconoir:input-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"MATERIAL\",\r\n                        \"label\": \"WMSInventory.MATERIAL\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"type\": \"text\",\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"clearable\": true,\r\n                        \"showPassword\": false,\r\n                        \"required\": true,\r\n                        \"requiredHint\": \"\",\r\n                        \"validation\": [\r\n                          \"noBlankStart\",\r\n                          \"noBlankEnd\"\r\n                        ],\r\n                        \"validationHint\": \"\",\r\n                        \"formatter\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": [],\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"minLength\": 0,\r\n                        \"maxLength\": 50,\r\n                        \"showWordLimit\": true,\r\n                        \"prefixIcon\": \"\",\r\n                        \"suffixIcon\": \"\",\r\n                        \"appendButton\": false,\r\n                        \"appendButtonDisabled\": false,\r\n                        \"buttonIcon\": \"\",\r\n                        \"iconType\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onInput\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onKeypressEnter\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"input45172\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol89625\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol89625\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"textarea\",\r\n                      \"icon\": \"bi:textarea-resize\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"MATERIAL_DESCRIPTION\",\r\n                        \"label\": \"WMSInventory.MATERIAL_DESCRIPTION\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"rows\": 3,\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"validation\": \"\",\r\n                        \"validationHint\": \"\",\r\n                        \"formatter\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": \"\",\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"minLength\": 0,\r\n                        \"maxLength\": 100,\r\n                        \"showWordLimit\": true,\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onInput\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"textarea27889\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol80661\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol80661\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"select\",\r\n                      \"icon\": \"svg-icon:select-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"FILTERTAGS\",\r\n                        \"label\": \"FilterTag.FilterTag\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"clearable\": true,\r\n                        \"filterable\": true,\r\n                        \"allowCreate\": false,\r\n                        \"remote\": false,\r\n                        \"multiple\": false,\r\n                        \"multipleLimit\": 0,\r\n                        \"lazy\": false,\r\n                        \"optionItems\": [\r\n                          {\r\n                            \"label\": \"select 1\",\r\n                            \"value\": \"1\"\r\n                          },\r\n                          {\r\n                            \"label\": \"select 2\",\r\n                            \"value\": \"2\"\r\n                          },\r\n                          {\r\n                            \"label\": \"select 3\",\r\n                            \"value\": \"3\"\r\n                          }\r\n                        ],\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"dataSource\": \"FILTERTAG\",\r\n                        \"optionTagName\": \"key\",\r\n                        \"optionValueName\": \"value\",\r\n                        \"trigger\": null,\r\n                        \"formatter\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": \"\",\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onRemoteQuery\": \"\",\r\n                        \"onRemoteQueryBefore\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"select103174\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol36277\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol36277\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"number\",\r\n                      \"icon\": \"svg-icon:number-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"MENGE\",\r\n                        \"label\": \"WMSInventory.MENGE\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"defaultValue\": null,\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"required\": true,\r\n                        \"requiredHint\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": \"\",\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"min\": -10000000000,\r\n                        \"max\": 100000000000,\r\n                        \"precision\": 2,\r\n                        \"step\": 1,\r\n                        \"controlsPosition\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"number86330\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol18428\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol18428\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"input\",\r\n                      \"icon\": \"iconoir:input-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"UNIT\",\r\n                        \"label\": \"WMSInventory.UNIT\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"type\": \"text\",\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"clearable\": true,\r\n                        \"showPassword\": false,\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"validation\": [\r\n                          \"noBlankStart\",\r\n                          \"noBlankEnd\"\r\n                        ],\r\n                        \"validationHint\": \"\",\r\n                        \"formatter\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": \"\",\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"minLength\": 0,\r\n                        \"maxLength\": 6,\r\n                        \"showWordLimit\": true,\r\n                        \"prefixIcon\": \"\",\r\n                        \"suffixIcon\": \"\",\r\n                        \"appendButton\": false,\r\n                        \"appendButtonDisabled\": false,\r\n                        \"buttonIcon\": \"\",\r\n                        \"iconType\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onInput\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onKeypressEnter\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"input72157\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol71331\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol71331\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"number\",\r\n                      \"icon\": \"svg-icon:number-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"ONLINE_QTY\",\r\n                        \"label\": \"WMSInventory.ONLINE_QTY\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"defaultValue\": null,\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": true,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": \"\",\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"min\": -10000000000,\r\n                        \"max\": 100000000000,\r\n                        \"precision\": 0,\r\n                        \"step\": 1,\r\n                        \"controlsPosition\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"number66265\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol67121\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol67121\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"input\",\r\n                      \"icon\": \"iconoir:input-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"MO_LOT_NUMBER\",\r\n                        \"label\": \"WMSPickingDetail.MO_LOT_NUMBER\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"type\": \"text\",\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"clearable\": true,\r\n                        \"showPassword\": false,\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"validation\": [\r\n                          \"noBlankStart\",\r\n                          \"noBlankEnd\"\r\n                        ],\r\n                        \"validationHint\": \"\",\r\n                        \"formatter\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": \"\",\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"minLength\": 0,\r\n                        \"maxLength\": 50,\r\n                        \"showWordLimit\": true,\r\n                        \"prefixIcon\": \"\",\r\n                        \"suffixIcon\": \"\",\r\n                        \"appendButton\": false,\r\n                        \"appendButtonDisabled\": false,\r\n                        \"buttonIcon\": \"\",\r\n                        \"iconType\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onInput\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onKeypressEnter\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"input109636\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol76212\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol76212\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"input\",\r\n                      \"icon\": \"iconoir:input-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"SO_LOT_NUMBER\",\r\n                        \"label\": \"WMSPickingDetail.SO_LOT_NUMBER\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"type\": \"text\",\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"clearable\": true,\r\n                        \"showPassword\": false,\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"validation\": [\r\n                          \"noBlankStart\",\r\n                          \"noBlankEnd\"\r\n                        ],\r\n                        \"validationHint\": \"\",\r\n                        \"formatter\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": \"\",\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"minLength\": 0,\r\n                        \"maxLength\": 300,\r\n                        \"showWordLimit\": true,\r\n                        \"prefixIcon\": \"\",\r\n                        \"suffixIcon\": \"\",\r\n                        \"appendButton\": false,\r\n                        \"appendButtonDisabled\": false,\r\n                        \"buttonIcon\": \"\",\r\n                        \"iconType\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onInput\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onKeypressEnter\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"input79753\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol99965\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol99965\"\r\n                },\r\n                {\r\n                  \"type\": \"grid-col\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"grid-col\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"type\": \"input\",\r\n                      \"icon\": \"iconoir:input-field\",\r\n                      \"formItemFlag\": true,\r\n                      \"options\": {\r\n                        \"name\": \"SUPPLIER_NAME\",\r\n                        \"label\": \"WMSPickingDetail.SUPPLIER_NAME\",\r\n                        \"labelAlign\": \"label-right-align\",\r\n                        \"type\": \"text\",\r\n                        \"defaultValue\": \"\",\r\n                        \"displayStyle\": \"block\",\r\n                        \"placeholder\": \"\",\r\n                        \"columnWidth\": 100,\r\n                        \"size\": \"\",\r\n                        \"labelWidth\": null,\r\n                        \"labelHidden\": false,\r\n                        \"readonly\": false,\r\n                        \"disabled\": false,\r\n                        \"hidden\": false,\r\n                        \"clearable\": true,\r\n                        \"showPassword\": false,\r\n                        \"required\": false,\r\n                        \"requiredHint\": \"\",\r\n                        \"validation\": [\r\n                          \"noBlankStart\",\r\n                          \"noBlankEnd\"\r\n                        ],\r\n                        \"validationHint\": \"\",\r\n                        \"formatter\": \"\",\r\n                        \"columnFixed\": false,\r\n                        \"columnFiltering\": false,\r\n                        \"columnSorting\": false,\r\n                        \"columnSortingType\": \"\",\r\n                        \"customClass\": \"\",\r\n                        \"labelFontFamily\": \"\",\r\n                        \"labelFontSize\": \"\",\r\n                        \"labelBold\": false,\r\n                        \"labelItalic\": false,\r\n                        \"labelUnderline\": false,\r\n                        \"labelLineThrough\": false,\r\n                        \"labelIconClass\": null,\r\n                        \"labelIconPosition\": \"rear\",\r\n                        \"labelTooltip\": null,\r\n                        \"labelIconType\": \"\",\r\n                        \"minLength\": 0,\r\n                        \"maxLength\": 100,\r\n                        \"showWordLimit\": true,\r\n                        \"prefixIcon\": \"\",\r\n                        \"suffixIcon\": \"\",\r\n                        \"appendButton\": false,\r\n                        \"appendButtonDisabled\": false,\r\n                        \"buttonIcon\": \"\",\r\n                        \"iconType\": \"\",\r\n                        \"onCreated\": \"\",\r\n                        \"onMounted\": \"\",\r\n                        \"onInput\": \"\",\r\n                        \"onChange\": \"\",\r\n                        \"onFocus\": \"\",\r\n                        \"onBlur\": \"\",\r\n                        \"onKeypressEnter\": \"\",\r\n                        \"onValidate\": \"\"\r\n                      },\r\n                      \"id\": \"input66787\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"gridcol4765\",\r\n                    \"hidden\": false,\r\n                    \"span\": 8,\r\n                    \"offset\": 0,\r\n                    \"push\": 0,\r\n                    \"pull\": 0,\r\n                    \"responsive\": false,\r\n                    \"md\": 12,\r\n                    \"sm\": 12,\r\n                    \"xs\": 12,\r\n                    \"customClass\": \"\",\r\n                    \"onCreated\": \"\",\r\n                    \"onMounted\": \"\"\r\n                  },\r\n                  \"id\": \"gridcol4765\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"gridForm\",\r\n                \"hidden\": false,\r\n                \"gutter\": 12,\r\n                \"colHeight\": null\r\n              },\r\n              \"id\": \"grid100462\"\r\n            },\r\n            {\r\n              \"type\": \"tab\",\r\n              \"category\": \"container\",\r\n              \"icon\": \"svg-icon:tab\",\r\n              \"tabs\": [\r\n                {\r\n                  \"type\": \"tab-pane\",\r\n                  \"category\": \"container\",\r\n                  \"icon\": \"tab-pane\",\r\n                  \"internal\": true,\r\n                  \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                  \"widgetList\": [\r\n                    {\r\n                      \"key\": 79014,\r\n                      \"type\": \"grid\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"ep:grid\",\r\n                      \"cols\": [\r\n                        {\r\n                          \"type\": \"grid-col\",\r\n                          \"category\": \"container\",\r\n                          \"icon\": \"grid-col\",\r\n                          \"internal\": true,\r\n                          \"draginable\": \"^(field-)|(container-(outside|internal)-(?!(dialog|drawer)$))[^/]+$\",\r\n                          \"widgetList\": [\r\n                            {\r\n                              \"type\": \"button\",\r\n                              \"icon\": \"svg-icon:button\",\r\n                              \"formItemFlag\": false,\r\n                              \"options\": {\r\n                                \"name\": \"btnAddDetail_VM_INVENTORY_DETAILs\",\r\n                                \"label\": \"common.add\",\r\n                                \"labelAlign\": \"\",\r\n                                \"columnWidth\": null,\r\n                                \"size\": \"small\",\r\n                                \"displayStyle\": \"inline-flex\",\r\n                                \"disabled\": false,\r\n                                \"hidden\": false,\r\n                                \"type\": \"success\",\r\n                                \"text\": false,\r\n                                \"plain\": false,\r\n                                \"round\": false,\r\n                                \"circle\": false,\r\n                                \"customClass\": [\r\n                                  \"pl-2\"\r\n                                ],\r\n                                \"labelFontFamily\": \"\",\r\n                                \"labelFontSize\": \"\",\r\n                                \"labelBold\": false,\r\n                                \"labelItalic\": false,\r\n                                \"labelUnderline\": false,\r\n                                \"labelLineThrough\": false,\r\n                                \"labelIconClass\": \"Plus\",\r\n                                \"labelIconPosition\": \"front\",\r\n                                \"labelTooltip\": null,\r\n                                \"labelIconType\": \"pl\",\r\n                                \"onCreated\": \"this.detailName='VM_INVENTORY_DETAILs'\",\r\n                                \"onMounted\": \"\",\r\n                                \"onClick\": \"//生成一行默认值\\n(this.getWidgetRef(this.detailName)).addToTableData();\"\r\n                              },\r\n                              \"id\": \"button36860\"\r\n                            },\r\n                            {\r\n                              \"type\": \"button\",\r\n                              \"icon\": \"svg-icon:button\",\r\n                              \"formItemFlag\": false,\r\n                              \"options\": {\r\n                                \"name\": \"btnDelDetail_VM_INVENTORY_DETAILs\",\r\n                                \"label\": \"common.delete\",\r\n                                \"labelAlign\": \"\",\r\n                                \"columnWidth\": null,\r\n                                \"size\": \"small\",\r\n                                \"displayStyle\": \"inline-flex\",\r\n                                \"disabled\": false,\r\n                                \"hidden\": false,\r\n                                \"type\": \"danger\",\r\n                                \"text\": false,\r\n                                \"plain\": false,\r\n                                \"round\": false,\r\n                                \"circle\": false,\r\n                                \"customClass\": [\r\n                                  \"pl-2\"\r\n                                ],\r\n                                \"labelFontFamily\": \"\",\r\n                                \"labelFontSize\": \"\",\r\n                                \"labelBold\": false,\r\n                                \"labelItalic\": false,\r\n                                \"labelUnderline\": false,\r\n                                \"labelLineThrough\": false,\r\n                                \"labelIconClass\": \"Minus\",\r\n                                \"labelIconPosition\": \"front\",\r\n                                \"labelTooltip\": null,\r\n                                \"labelIconType\": \"pl\",\r\n                                \"onCreated\": \"this.detailName='VM_INVENTORY_DETAILs'\",\r\n                                \"onMounted\": \"\",\r\n                                \"onClick\": \"const rows = (this.getWidgetRef(this.detailName)).getSelectionRows();\\nif (!rows || rows.length < 1) {\\n  return useMessage.error(this.$t('common.selectNoData', [this.$t('common.delete')]));\\n}\\nrows.forEach(row => {\\n  (this.getWidgetRef(this.detailName)).deleteTableData(row);\\n})\"\r\n                              },\r\n                              \"id\": \"button42081\"\r\n                            },\r\n                            {\r\n                              \"type\": \"button\",\r\n                              \"icon\": \"svg-icon:button\",\r\n                              \"formItemFlag\": false,\r\n                              \"options\": {\r\n                                \"name\": \"btnRefreshDetail_VM_INVENTORY_DETAILs\",\r\n                                \"label\": \"common.refresh\",\r\n                                \"labelAlign\": \"\",\r\n                                \"columnWidth\": null,\r\n                                \"size\": \"small\",\r\n                                \"displayStyle\": \"inline-flex\",\r\n                                \"disabled\": false,\r\n                                \"hidden\": false,\r\n                                \"type\": \"primary\",\r\n                                \"text\": false,\r\n                                \"plain\": true,\r\n                                \"round\": false,\r\n                                \"circle\": false,\r\n                                \"customClass\": [\r\n                                  \"pl-2\"\r\n                                ],\r\n                                \"labelFontFamily\": \"\",\r\n                                \"labelFontSize\": \"\",\r\n                                \"labelBold\": false,\r\n                                \"labelItalic\": false,\r\n                                \"labelUnderline\": false,\r\n                                \"labelLineThrough\": false,\r\n                                \"labelIconClass\": \"Refresh\",\r\n                                \"labelIconPosition\": \"front\",\r\n                                \"labelTooltip\": null,\r\n                                \"labelIconType\": \"pl\",\r\n                                \"onCreated\": \"this.detailName='VM_INVENTORY_DETAILs'\",\r\n                                \"onMounted\": \"\",\r\n                                \"onClick\": \"//表格的重新加载\\n(this.getWidgetRef(this.detailName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/true)\\n\"\r\n                              },\r\n                              \"id\": \"button119515\"\r\n                            },\r\n                            {\r\n                              \"type\": \"button\",\r\n                              \"icon\": \"svg-icon:button\",\r\n                              \"formItemFlag\": false,\r\n                              \"options\": {\r\n                                \"name\": \"btnAdjustDetail_VM_INVENTORY_DETAILs\",\r\n                                \"label\": \"common.adjustColumns\",\r\n                                \"labelAlign\": \"\",\r\n                                \"columnWidth\": null,\r\n                                \"size\": \"small\",\r\n                                \"displayStyle\": \"inline-flex\",\r\n                                \"disabled\": false,\r\n                                \"hidden\": false,\r\n                                \"type\": \"warning\",\r\n                                \"text\": false,\r\n                                \"plain\": true,\r\n                                \"round\": false,\r\n                                \"circle\": false,\r\n                                \"customClass\": [\r\n                                  \"pl-2\"\r\n                                ],\r\n                                \"labelFontFamily\": \"\",\r\n                                \"labelFontSize\": \"\",\r\n                                \"labelBold\": false,\r\n                                \"labelItalic\": false,\r\n                                \"labelUnderline\": false,\r\n                                \"labelLineThrough\": false,\r\n                                \"labelIconClass\": \"el-icon-set-up\",\r\n                                \"labelIconPosition\": \"front\",\r\n                                \"labelTooltip\": null,\r\n                                \"labelIconType\": \"el\",\r\n                                \"onCreated\": \"this.detailName='VM_INVENTORY_DETAILs'\",\r\n                                \"onMounted\": \"\",\r\n                                \"onClick\": \"//表格的列调整\\nconst $table = this.getWidgetRef(this.detailName);\\n$table.openColumnDrawer();\\n\"\r\n                              },\r\n                              \"id\": \"button106086\"\r\n                            }\r\n                          ],\r\n                          \"options\": {\r\n                            \"name\": \"gridcol112520\",\r\n                            \"hidden\": true,\r\n                            \"span\": 24,\r\n                            \"offset\": 0,\r\n                            \"push\": 0,\r\n                            \"pull\": 0,\r\n                            \"responsive\": false,\r\n                            \"md\": 12,\r\n                            \"sm\": 12,\r\n                            \"xs\": 12,\r\n                            \"customClass\": [\r\n                              \"flex\",\r\n                              \"justify-end\"\r\n                            ]\r\n                          },\r\n                          \"id\": \"gridcol106232\"\r\n                        }\r\n                      ],\r\n                      \"options\": {\r\n                        \"name\": \"grid115896\",\r\n                        \"hidden\": false,\r\n                        \"gutter\": 12,\r\n                        \"colHeight\": null,\r\n                        \"customClass\": [\r\n                          \"mb-2\"\r\n                        ]\r\n                      },\r\n                      \"id\": \"grid54404\"\r\n                    },\r\n                    {\r\n                      \"key\": 27644,\r\n                      \"type\": \"data-table\",\r\n                      \"category\": \"container\",\r\n                      \"icon\": \"svg-icon:data-table\",\r\n                      \"draginable\": \"^field-(formItem|static)-(?!(html-text|divider|steps|transfer|pagination|alert|markdown-editor|rich-editor)$)[^/]+$\",\r\n                      \"widgetList\": [\r\n                        {\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"系统\",\r\n                            \"label\": \"系统\",\r\n                            \"labelAlign\": \"label-center-align\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 110,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": true,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"formatter\": \"\",\r\n                            \"columnFixed\": false,\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": \"\",\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 6,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input31184\"\r\n                        },\r\n                        {\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"动作\",\r\n                            \"label\": \"动作\",\r\n                            \"labelAlign\": \"label-center-align\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 110,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": true,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"formatter\": \"\",\r\n                            \"columnFixed\": false,\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": \"\",\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 4,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input81645\"\r\n                        },\r\n                        {\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"排序\",\r\n                            \"label\": \"ColumnAdjust.Order\",\r\n                            \"labelAlign\": \"label-center-align\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 110,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"formatter\": \"\",\r\n                            \"columnFixed\": false,\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": \"\",\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 8,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input28963\"\r\n                        },\r\n                        {\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"日期\",\r\n                            \"label\": \"日期\",\r\n                            \"labelAlign\": \"label-center-align\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 110,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"formatter\": \"\",\r\n                            \"columnFixed\": false,\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": \"\",\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 8,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input48859\"\r\n                        },\r\n                        {\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"单号\",\r\n                            \"label\": \"WMSCheckingHeader.ORDER_NO\",\r\n                            \"labelAlign\": \"label-center-align\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 110,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": true,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"formatter\": \"\",\r\n                            \"columnFixed\": false,\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": \"\",\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 50,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input40520\"\r\n                        },\r\n                        {\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"物料\",\r\n                            \"label\": \"WMSInventory.MATERIAL\",\r\n                            \"labelAlign\": \"label-center-align\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 110,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"formatter\": \"\",\r\n                            \"columnFixed\": false,\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": \"\",\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 50,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input55295\"\r\n                        },\r\n                        {\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"数量\",\r\n                            \"label\": \"WMSInventory.MENGE\",\r\n                            \"labelAlign\": \"label-center-align\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 110,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"formatter\": \"\",\r\n                            \"columnFixed\": false,\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": \"\",\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 9,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input69788\"\r\n                        },\r\n                        {\r\n                          \"type\": \"input\",\r\n                          \"icon\": \"iconoir:input-field\",\r\n                          \"formItemFlag\": true,\r\n                          \"options\": {\r\n                            \"name\": \"工厂\",\r\n                            \"label\": \"Factory.Name\",\r\n                            \"labelAlign\": \"label-center-align\",\r\n                            \"type\": \"text\",\r\n                            \"defaultValue\": \"\",\r\n                            \"displayStyle\": \"block\",\r\n                            \"placeholder\": \"\",\r\n                            \"columnWidth\": 110,\r\n                            \"size\": \"\",\r\n                            \"labelWidth\": null,\r\n                            \"labelHidden\": false,\r\n                            \"readonly\": false,\r\n                            \"disabled\": false,\r\n                            \"hidden\": false,\r\n                            \"clearable\": true,\r\n                            \"showPassword\": false,\r\n                            \"required\": false,\r\n                            \"requiredHint\": \"\",\r\n                            \"validation\": [\r\n                              \"noBlankStart\",\r\n                              \"noBlankEnd\"\r\n                            ],\r\n                            \"validationHint\": \"\",\r\n                            \"formatter\": \"\",\r\n                            \"columnFixed\": false,\r\n                            \"columnFiltering\": false,\r\n                            \"columnSorting\": false,\r\n                            \"columnSortingType\": \"\",\r\n                            \"customClass\": \"\",\r\n                            \"labelFontFamily\": \"\",\r\n                            \"labelFontSize\": \"\",\r\n                            \"labelBold\": false,\r\n                            \"labelItalic\": false,\r\n                            \"labelUnderline\": false,\r\n                            \"labelLineThrough\": false,\r\n                            \"labelIconClass\": null,\r\n                            \"labelIconPosition\": \"rear\",\r\n                            \"labelTooltip\": null,\r\n                            \"labelIconType\": \"\",\r\n                            \"minLength\": 0,\r\n                            \"maxLength\": 30,\r\n                            \"showWordLimit\": false,\r\n                            \"prefixIcon\": \"\",\r\n                            \"suffixIcon\": \"\",\r\n                            \"appendButton\": false,\r\n                            \"appendButtonDisabled\": false,\r\n                            \"buttonIcon\": \"\",\r\n                            \"iconType\": \"\",\r\n                            \"onCreated\": \"\",\r\n                            \"onMounted\": \"\",\r\n                            \"onInput\": \"\",\r\n                            \"onChange\": \"\",\r\n                            \"onFocus\": \"\",\r\n                            \"onBlur\": \"\",\r\n                            \"onKeypressEnter\": \"\",\r\n                            \"onValidate\": \"\"\r\n                          },\r\n                          \"id\": \"input62628\"\r\n                        }\r\n                      ],\r\n                      \"options\": {\r\n                        \"name\": \"VM_INVENTORY_DETAILs\",\r\n                        \"hidden\": false,\r\n                        \"rowSpacing\": 8,\r\n                        \"height\": 300,\r\n                        \"dataSourceUrl\": \"VM_Inventory_Detail/getPageData\",\r\n                        \"resultPath\": \"data.rows\",\r\n                        \"disabled\": false,\r\n                        \"readonly\": true,\r\n                        \"loadTreeData\": false,\r\n                        \"rowKey\": \"__row_key\",\r\n                        \"childrenKey\": \"children\",\r\n                        \"stripe\": true,\r\n                        \"showIndex\": true,\r\n                        \"showCheckBox\": true,\r\n                        \"paging\": true,\r\n                        \"smallPagination\": false,\r\n                        \"border\": true,\r\n                        \"size\": \"default\",\r\n                        \"pagination\": {\r\n                          \"currentPage\": 1,\r\n                          \"pageSizes\": [\r\n                            10,\r\n                            15,\r\n                            20,\r\n                            30,\r\n                            50,\r\n                            100,\r\n                            200\r\n                          ],\r\n                          \"pageSize\": 20,\r\n                          \"total\": 366,\r\n                          \"pagerCount\": 5\r\n                        },\r\n                        \"defaultValue\": [\r\n                          {}\r\n                        ],\r\n                        \"onLoadBefore\": \"const formData = this.formModel;\\r\\nif (formData.__optype === 'A') {\\r\\n  //不执行后续的查询\\r\\n  return false\\r\\n} else if (formData.__optype === 'U') {\\r\\n  const parentRef = this.getFormRef().parentFormRef;\\r\\n  this.permissionTable = parentRef.tableInfo.tableName;\\r\\n  param.wheres.push({ name: this.foreignKey, value: formData[\\\"MATERIAL\\\"], displayType: '=' })\\r\\n  param.orderbys = [{ sort: \\\"排序\\\", order: \\\"asc\\\" }]\\r\\n}\",\r\n                        \"onPageSizeChange\": \"\",\r\n                        \"onCurrentPageChange\": \"\",\r\n                        \"onSelectionChange\": \"\",\r\n                        \"onHideOperationButton\": \"\",\r\n                        \"onDisableOperationButton\": \"\",\r\n                        \"onGetOperationButtonLabel\": \"\",\r\n                        \"onOperationButtonClick\": \"\",\r\n                        \"onHeaderClick\": \"\",\r\n                        \"onRowClick\": \"\",\r\n                        \"onRowDoubleClick\": \"\",\r\n                        \"onCellClick\": \"\",\r\n                        \"onCellDoubleClick\": \"\",\r\n                        \"onGetRowClassName\": \"\",\r\n                        \"onGetSpanMethod\": \"\",\r\n                        \"label\": \"data-table\",\r\n                        \"onlyChangedData\": true,\r\n                        \"onCreated\": \"this.foreignKey = '物料';\"\r\n                      },\r\n                      \"id\": \"datatable109857\"\r\n                    }\r\n                  ],\r\n                  \"options\": {\r\n                    \"name\": \"tabpane20341\",\r\n                    \"label\": \"库存明细\",\r\n                    \"disabled\": false,\r\n                    \"lazy\": false,\r\n                    \"closable\": false,\r\n                    \"hidden\": false\r\n                  },\r\n                  \"id\": \"tabpane58712\"\r\n                }\r\n              ],\r\n              \"options\": {\r\n                \"name\": \"tabDetail\",\r\n                \"hidden\": false,\r\n                \"tabType\": \"border-card\",\r\n                \"tabPosition\": \"top\",\r\n                \"defaultValue\": \"tabpane_detail\"\r\n              },\r\n              \"id\": \"tab70737\"\r\n            }\r\n          ],\r\n          \"options\": {\r\n            \"name\": \"dialogbody65181\",\r\n            \"hidden\": false,\r\n            \"form\": {\r\n              \"size\": \"\",\r\n              \"labelPosition\": \"left\",\r\n              \"labelAlign\": \"label-left-align\",\r\n              \"labelWidth\": \"auto\",\r\n              \"customClass\": []\r\n            },\r\n            \"label\": \"dialog-body\",\r\n            \"columnHeight\": null\r\n          },\r\n          \"id\": \"dialogbody65181\"\r\n        }\r\n      ],\r\n      \"options\": {\r\n        \"name\": \"dialogEditForm\",\r\n        \"label\": \"dialog\",\r\n        \"center\": false,\r\n        \"showClose\": true,\r\n        \"columnWidth\": 50,\r\n        \"draggable\": true,\r\n        \"top\": \"15px\",\r\n        \"fullscreen\": true,\r\n        \"onOpenBefore\": \"\",\r\n        \"onOpenAfter\": \"this.setTimeout(() => {\\r\\n  this.getBodyFormRef()?.setReadonlyMode(true);\\r\\n}, 1000);\",\r\n        \"onSubmit\": \"const data = await this.getValue();\\nconst formData = {\\n  mainData: data\\n}\\nconst rootForm = this.getFormRef();\\nif (['A', 'U'].includes(data.__optype)) {\\n  const { http } = rootForm.commonApi();\\n  const res = await http(data.__optype === 'A' ? rootForm.getUrl('add2', true) : data.__optype === 'U' ? rootForm.getUrl('update2', true) : '', 'post', { data: formData });\\n  if (!!res?.status) {\\n    rootForm.useMessage.success(res.message);\\n    //表格的重新加载\\n    (this.getWidgetRef(rootForm.tableInfo.tableName)).initTableData(/*额外查询条件*/undefined,/*重置页码*/true,/*重置列筛选*/false);\\n  } else {\\n    return false;\\n  }\\n}\\n\",\r\n        \"onCloseBefore\": \"\",\r\n        \"onCloseAfter\": \"const parentRef= this.getFormRef();\\r\\nparentRef.setFormData({'dialogEditForm':undefined});\",\r\n        \"destroyOnClose\": true\r\n      },\r\n      \"id\": \"dialog44798\"\r\n    }\r\n  ],\r\n  \"formConfig\": {\r\n    \"name\": \"vForm102307\",\r\n    \"modelName\": \"formData\",\r\n    \"refName\": \"vForm\",\r\n    \"rulesName\": \"rules\",\r\n    \"labelWidth\": 80,\r\n    \"labelPosition\": \"left\",\r\n    \"size\": \"small\",\r\n    \"labelAlign\": \"label-left-align\",\r\n    \"cssCode\": \".itemAlignTop .field-widget-item {\\r\\n   vertical-align: top;\\r\\n}\\r\\n\\r\\n.linkColorBlack>.el-link {\\r\\n   --el-link-text-color: var(--el-text-color-primary);\\r\\n}\\r\\n\\r\\n.mb-0{\\r\\n  margin-bottom: 0 !important;\\r\\n}\",\r\n    \"customClass\": [\r\n      \"px-15px\"\r\n    ],\r\n    \"functions\": \"\",\r\n    \"layoutType\": \"PC\",\r\n    \"jsonVersion\": 3,\r\n    \"disabled\": false,\r\n    \"readonly\": false,\r\n    \"entityName\": \"WMS_Inventory\",\r\n    \"dicNoList\": [],\r\n    \"onFormCreated\": \"this.tableInfo={\\\"tableName\\\":\\\"WMS_Inventory\\\",\\\"columnCNName\\\":\\\"库存\\\",\\\"tableKey\\\":\\\"ID\\\",\\\"expressField\\\":\\\"MATERIAL\\\"};\\n      this.getUrl=(action,ignoreSuffix)=>'WMS_Inventory/'+action+(!!!ignoreSuffix?'':'');\\n      this.onInit();\",\r\n    \"onFormMounted\": \"this.onInited();\",\r\n    \"onFormDataChange\": \"\",\r\n    \"labelFontFamily\": null,\r\n    \"labelFontSize\": null,\r\n    \"lazyDicNoList\": [],\r\n    \"optionItemsObject\": []\r\n  }\r\n}","FORMCONFIG":null,"FORMFIELDS":null,"TABLECONFIG":null,"CREATOR":"超级管理员","CREATEDATE":"2024-07-01T00:00:00","CREATEID":1,"MODIFIER":"超级管理员","MODIFYDATE":"2024-08-05T00:00:00","MODIFYID":1,"FORMSTATUS":0,"FORMREVISION":"WMS_Inventory","__optype":null}],"summary":null,"extra":null}}|| || 00-421fb41c3428cfd8ba5197bd1423c2e3-37bced0a6abd5875-00 ||end
2024-08-05 09:54:56.9610||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-d327a58baa21848e0ffb03e086ec92f7-c98fb96bbd81417a-00 ||end
2024-08-05 09:54:56.9610||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (3ms) [Parameters=[@__userInfo_User_Id_0='?' (DbType = Int32), @__apiColumn_tableName_1='?' (Size = 100)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[SYS_COLUMNADJUSTMENTID], [s].[COLUMNJSON], [s].[CREATEDATE], [s].[CREATEID], [s].[CREATOR], [s].[TABLENAME], [s].[USER_ID]
FROM [SYS_COLUMNADJUSTMENT] AS [s]
WHERE ([s].[USER_ID] = @__userInfo_User_Id_0) AND ([s].[TABLENAME] = @__apiColumn_tableName_1)|| || 00-d327a58baa21848e0ffb03e086ec92f7-c98fb96bbd81417a-00 ||end
2024-08-05 09:54:56.9610||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-d327a58baa21848e0ffb03e086ec92f7-c98fb96bbd81417a-00 ||end
2024-08-05 09:54:56.9610||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.ColumnController.Read (ZT.WebApi)|| || 00-d327a58baa21848e0ffb03e086ec92f7-c98fb96bbd81417a-00 ||end
2024-08-05 09:54:56.9610||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"tableName":"WMS_Inventory-datatable68018"}|| || 00-d327a58baa21848e0ffb03e086ec92f7-c98fb96bbd81417a-00 ||end
2024-08-05 09:54:56.9610||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":"ResponseType.QuerySuccess","data":null}|| || 00-d327a58baa21848e0ffb03e086ec92f7-c98fb96bbd81417a-00 ||end
2024-08-05 09:54:57.5858||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-12e280407ff8cd4afcdbc52c55714745-3d92751be438018f-00 ||end
2024-08-05 09:54:57.5858||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_DictionaryController.GetVueDictionary (ZT.WebApi)|| || 00-12e280407ff8cd4afcdbc52c55714745-3d92751be438018f-00 ||end
2024-08-05 09:54:57.5858||Debug||ZT.Core.Filters.ResultFilter||RequestData: [{"DicNo":"FILTERTAG"}]|| || 00-12e280407ff8cd4afcdbc52c55714745-3d92751be438018f-00 ||end
2024-08-05 09:54:57.5858||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":null,"data":[{"total":10,"data":[{"KEY":6,"VALUE":"1组"},{"KEY":7,"VALUE":"2组"},{"KEY":3,"VALUE":"AAA"},{"KEY":4,"VALUE":"fdwaf"},{"KEY":5,"VALUE":"ffggg"},{"KEY":8,"VALUE":"M4"},{"KEY":9,"VALUE":"M5"},{"KEY":10,"VALUE":"M6"},{"KEY":1,"VALUE":"T1"},{"KEY":2,"VALUE":"T2"}],"config":null,"dicNo":"FILTERTAG"}]}|| || 00-12e280407ff8cd4afcdbc52c55714745-3d92751be438018f-00 ||end
2024-08-05 09:54:57.5858||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-942827efc74ecf88e9c0455056a6c25a-1249aa87b45161d3-00 ||end
2024-08-05 09:54:57.6019||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (15ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM [WMS_INVENTORY] AS [w]|| || 00-942827efc74ecf88e9c0455056a6c25a-1249aa87b45161d3-00 ||end
2024-08-05 09:54:57.6163||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (13ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT [w].[ID], [w].[FILTERTAGS], [w].[MATERIAL], [w].[MATERIAL_DESCRIPTION], [w].[MENGE], [w].[MO_LOT_NUMBER], [w].[SO_LOT_NUMBER], [w].[SUPPLIER_NAME], [w].[UNIT]
FROM [WMS_INVENTORY] AS [w]
ORDER BY [w].[ID] DESC
OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY|| || 00-942827efc74ecf88e9c0455056a6c25a-1249aa87b45161d3-00 ||end
2024-08-05 09:54:58.0699||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-942827efc74ecf88e9c0455056a6c25a-1249aa87b45161d3-00 ||end
2024-08-05 09:54:58.0699||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.Warehouse.Controllers.WMS_InventoryController.GetPageData (ZT.WebApi)|| || 00-942827efc74ecf88e9c0455056a6c25a-1249aa87b45161d3-00 ||end
2024-08-05 09:54:58.0699||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"page":1,"rows":30,"wheres":"[]","orderbys":[]}|| || 00-942827efc74ecf88e9c0455056a6c25a-1249aa87b45161d3-00 ||end
2024-08-05 09:54:58.0699||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":"ResponseType.QuerySuccess","data":{"status":true,"msg":null,"total":39,"rows":[{"ID":47,"MATERIAL":"1100001051W","MATERIAL_DESCRIPTION":"CHS3xyz负极0.25mHCB40开博扬杰30SQ045-SL双认证","MENGE":12086.0000,"UNIT":"PCS","ONLINE_QTY":13.0,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":46,"MATERIAL":"1100000754W","MATERIAL_DESCRIPTION":"CHS3xyz 三分体正极0.35mHCB40开博扬杰30SQ045-SL双认证","MENGE":12000.0000,"UNIT":"PCS","ONLINE_QTY":null,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":45,"MATERIAL":"1110000671","MATERIAL_DESCRIPTION":"短边框1134*30*13.5mm U6.1 T6 A10","MENGE":12000.0000,"UNIT":"PCS","ONLINE_QTY":2033.0,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":44,"MATERIAL":"1110000670","MATERIAL_DESCRIPTION":"长边框2278*30*30mm U6.1溢胶槽4孔 宽型腔T6 A10","MENGE":12000.0000,"UNIT":"PCS","ONLINE_QTY":3966.0,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":43,"MATERIAL":"1100001518","MATERIAL_DESCRIPTION":"CHS3xyz正0.4\\HCB40\\开博扬杰30SQ045-SL双认证","MENGE":1000.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":42,"MATERIAL":"1100001492","MATERIAL_DESCRIPTION":"CHS3xyz负0.2\\HCB40\\开博扬杰30SQ045-SL双认证","MENGE":1000.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":41,"MATERIAL":"1100000753","MATERIAL_DESCRIPTION":"CHS3xyz三分体中低座扬杰30SQ045-SL-双认证","MENGE":13000.0000,"UNIT":null,"ONLINE_QTY":50.0,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":40,"MATERIAL":"1100000743","MATERIAL_DESCRIPTION":"CHS3xyz三分体接线盒盒盖","MENGE":39000.0000,"UNIT":null,"ONLINE_QTY":15005.0,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":39,"MATERIAL":"1110000800","MATERIAL_DESCRIPTION":"短边框1134*30*13.5mm U6.2 T6 A10NT优化","MENGE":4500.0000,"UNIT":"PCS","ONLINE_QTY":null,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":38,"MATERIAL":"1110000798","MATERIAL_DESCRIPTION":"长边框2278*30*30mm U6.2 4孔T6 A10NT优化","MENGE":6000.0000,"UNIT":"PCS","ONLINE_QTY":null,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":37,"MATERIAL":"1060000659","MATERIAL_DESCRIPTION":"1060000659","MENGE":12906.0000,"UNIT":null,"ONLINE_QTY":101027766913.8574,"FILTERTAGS":"8","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":36,"MATERIAL":"mmmm","MATERIAL_DESCRIPTION":null,"MENGE":14.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":"7","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":35,"MATERIAL":"5455","MATERIAL_DESCRIPTION":null,"MENGE":2.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":"7","MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":33,"MATERIAL":"1070000229","MATERIAL_DESCRIPTION":null,"MENGE":-113.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":32,"MATERIAL":"2000004380W","MATERIAL_DESCRIPTION":"2000004380W","MENGE":1000.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":31,"MATERIAL":"2000004305W","MATERIAL_DESCRIPTION":"2000004305W","MENGE":2183.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":29,"MATERIAL":"1050000209","MATERIAL_DESCRIPTION":"1050000209","MENGE":1800.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":28,"MATERIAL":"3150000090W","MATERIAL_DESCRIPTION":"3150000090W","MENGE":996.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":27,"MATERIAL":"1070000232","MATERIAL_DESCRIPTION":"1070000232","MENGE":1399.5548,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":26,"MATERIAL":"1060000660","MATERIAL_DESCRIPTION":"1060000660","MENGE":2591.3197,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":25,"MATERIAL":"1070000231","MATERIAL_DESCRIPTION":"1070000231","MENGE":1699.6115,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":24,"MATERIAL":"1070000224","MATERIAL_DESCRIPTION":"1070000224","MENGE":1399.8158,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":23,"MATERIAL":"1060000635","MATERIAL_DESCRIPTION":"1060000635","MENGE":1497.9223,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":22,"MATERIAL":"1060000659","MATERIAL_DESCRIPTION":"1060000659","MENGE":942.6323,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":21,"MATERIAL":"1050000247","MATERIAL_DESCRIPTION":"1050000247","MENGE":318.9978,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null},{"ID":20,"MATERIAL":"1110000838","MATERIAL_DESCRIPTION":"长边框2278*30*28mm U6.2 6孔T6 A10","MENGE":89888.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":"1111","SUPPLIER_NAME":"大鱼","__optype":null},{"ID":19,"MATERIAL":"mmmm","MATERIAL_DESCRIPTION":"时间分厘卡但是","MENGE":18.0000,"UNIT":"kkk","ONLINE_QTY":null,"FILTERTAGS":"6","MO_LOT_NUMBER":"uuuu","SO_LOT_NUMBER":"范德萨","SUPPLIER_NAME":"更多时间留给弗利萨","__optype":null},{"ID":18,"MATERIAL":"hfdsgd","MATERIAL_DESCRIPTION":null,"MENGE":2.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":"kkkkk","SUPPLIER_NAME":null,"__optype":null},{"ID":17,"MATERIAL":"55555","MATERIAL_DESCRIPTION":"ttttt","MENGE":13.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":"llllmmm","SO_LOT_NUMBER":null,"SUPPLIER_NAME":"ssssss","__optype":null},{"ID":14,"MATERIAL":"1","MATERIAL_DESCRIPTION":"2","MENGE":1.0000,"UNIT":null,"ONLINE_QTY":null,"FILTERTAGS":null,"MO_LOT_NUMBER":null,"SO_LOT_NUMBER":null,"SUPPLIER_NAME":null,"__optype":null}],"summary":null,"extra":null}}|| || 00-942827efc74ecf88e9c0455056a6c25a-1249aa87b45161d3-00 ||end
2024-08-05 09:54:59.3541||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-17769539462ddb6d1bbbf760c22f14d2-79544b90e789f1a8-00 ||end
2024-08-05 09:54:59.3541||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (5ms) [Parameters=[@__userInfo_User_Id_0='?' (DbType = Int32), @__apiColumn_tableName_1='?' (Size = 100)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[SYS_COLUMNADJUSTMENTID], [s].[COLUMNJSON], [s].[CREATEDATE], [s].[CREATEID], [s].[CREATOR], [s].[TABLENAME], [s].[USER_ID]
FROM [SYS_COLUMNADJUSTMENT] AS [s]
WHERE ([s].[USER_ID] = @__userInfo_User_Id_0) AND ([s].[TABLENAME] = @__apiColumn_tableName_1)|| || 00-17769539462ddb6d1bbbf760c22f14d2-79544b90e789f1a8-00 ||end
2024-08-05 09:54:59.3541||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-17769539462ddb6d1bbbf760c22f14d2-79544b90e789f1a8-00 ||end
2024-08-05 09:54:59.3541||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.ColumnController.Read (ZT.WebApi)|| || 00-17769539462ddb6d1bbbf760c22f14d2-79544b90e789f1a8-00 ||end
2024-08-05 09:54:59.3541||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"tableName":"VM_INVENTORY_DETAILs-datatable109857"}|| || 00-17769539462ddb6d1bbbf760c22f14d2-79544b90e789f1a8-00 ||end
2024-08-05 09:54:59.3541||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":"ResponseType.QuerySuccess","data":null}|| || 00-17769539462ddb6d1bbbf760c22f14d2-79544b90e789f1a8-00 ||end
2024-08-05 09:54:59.7830||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-0e02768c0cb82213c411fe9c9d36bc3e-89959a4d73772987-00 ||end
2024-08-05 09:54:59.7830||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.System.Controllers.Sys_DictionaryController.GetVueDictionary (ZT.WebApi)|| || 00-0e02768c0cb82213c411fe9c9d36bc3e-89959a4d73772987-00 ||end
2024-08-05 09:54:59.7830||Debug||ZT.Core.Filters.ResultFilter||RequestData: [{"DicNo":"FILTERTAG"}]|| || 00-0e02768c0cb82213c411fe9c9d36bc3e-89959a4d73772987-00 ||end
2024-08-05 09:54:59.7830||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":null,"data":[{"total":10,"data":[{"KEY":6,"VALUE":"1组"},{"KEY":7,"VALUE":"2组"},{"KEY":3,"VALUE":"AAA"},{"KEY":4,"VALUE":"fdwaf"},{"KEY":5,"VALUE":"ffggg"},{"KEY":8,"VALUE":"M4"},{"KEY":9,"VALUE":"M5"},{"KEY":10,"VALUE":"M6"},{"KEY":1,"VALUE":"T1"},{"KEY":2,"VALUE":"T2"}],"config":null,"dicNo":"FILTERTAG"}]}|| || 00-0e02768c0cb82213c411fe9c9d36bc3e-89959a4d73772987-00 ||end
2024-08-05 09:54:59.8700||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-20ca8d9773dd5e225ab2097db4da1981-4fbc2665f75c4383-00 ||end
2024-08-05 09:54:59.8700||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (4ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM [VM_INVENTORY_DETAIL] AS [v]
WHERE [v].[物料] = '1100001051W'|| || 00-20ca8d9773dd5e225ab2097db4da1981-4fbc2665f75c4383-00 ||end
2024-08-05 09:54:59.8825||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (7ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT [v].[单号], [v].[动作], [v].[工厂], [v].[排序], [v].[数量], [v].[日期], [v].[物料], [v].[系统]
FROM [VM_INVENTORY_DETAIL] AS [v]
WHERE [v].[物料] = '1100001051W'
ORDER BY [v].[排序]
OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY|| || 00-20ca8d9773dd5e225ab2097db4da1981-4fbc2665f75c4383-00 ||end
2024-08-05 09:54:59.8825||Debug||ZT.Core.Filters.ResultFilter||User: {"User_Id":1,"Role_Id":[1],"RoleName":null,"UserName":"admin","UserTrueName":"超级管理员","Enable":1,"Token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************.GMcIdSuWs5E3fTvMDLRp_1FsY7HR-jZyTgppgTbAxBo","FILTERTAGS":null,"FILTERTAGACCESS":"3","UserPwd":"QBW3LRusCG_BT9xUNXAG6g==","Employeeid":null,"Factory":{"id":"2","name":"ZT01","revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"WorkCenter":{"id":null,"name":null,"revision":null,"level":null,"isRevofRcd":null,"isFrozen":null,"status":null,"isNDO":false,"isRDO":false,"objectType":null,"parent":null,"baseId":null},"Operation":null}|| || 00-20ca8d9773dd5e225ab2097db4da1981-4fbc2665f75c4383-00 ||end
2024-08-05 09:54:59.8825||Debug||ZT.Core.Filters.ResultFilter||Action: ZT.Warehouse.Controllers.VM_Inventory_DetailController.GetPageData (ZT.WebApi)|| || 00-20ca8d9773dd5e225ab2097db4da1981-4fbc2665f75c4383-00 ||end
2024-08-05 09:54:59.8825||Debug||ZT.Core.Filters.ResultFilter||RequestData: {"page":1,"rows":30,"wheres":"[{\"name\":\"物料\",\"value\":\"1100001051W\",\"displayType\":\"=\"}]","orderbys":[{"sort":"排序","order":"asc"}]}|| || 00-20ca8d9773dd5e225ab2097db4da1981-4fbc2665f75c4383-00 ||end
2024-08-05 09:54:59.8825||Debug||ZT.Core.Filters.ResultFilter||ResponseData: {"status":true,"code":"323","message":"ResponseType.QuerySuccess","data":{"status":true,"msg":null,"total":4,"rows":[{"系统":"WMS","动作":"发料","排序":302,"日期":"2024-08-01T11:03:07.537","单号":"PT2480001","物料":"1100001051W","数量":12000.0000,"工厂":"M4","__optype":null},{"系统":"MES","动作":"上料","排序":307,"日期":"2024-08-01T13:46:53.983","单号":"1282000076","物料":"1100001051W","数量":-1.0000,"工厂":"M4","__optype":null},{"系统":"MES","动作":"上料","排序":308,"日期":"2024-08-01T15:17:50.897","单号":"1282000076","物料":"1100001051W","数量":-12.0000,"工厂":"M4","__optype":null},{"系统":"线边仓","动作":"损耗","排序":309,"日期":"2024-08-01T16:12:44.093","单号":"8d6d0a00-2481-4f7b-8667-3ef0b603d635","物料":"1100001051W","数量":99.0000,"工厂":"M4","__optype":null}],"summary":null,"extra":null}}|| || 00-20ca8d9773dd5e225ab2097db4da1981-4fbc2665f75c4383-00 ||end
2024-08-05 09:55:15.2629||Info||Microsoft.EntityFrameworkCore.Infrastructure||Entity Framework Core 6.0.29 initialized 'VOLContext' using provider 'Microsoft.EntityFrameworkCore.SqlServer:6.0.29' with options: NoTracking || || 00-59b7bb595a9fd73b2877e13257e9ffe3-c511c3ca3ba446c0-00 ||end
2024-08-05 09:55:15.2629||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (8ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
SELECT COUNT(*)
FROM [Sys_FormDesign_Template] AS [s]|| || 00-59b7bb595a9fd73b2877e13257e9ffe3-c511c3ca3ba446c0-00 ||end
2024-08-05 09:55:15.2864||Info||Microsoft.EntityFrameworkCore.Database.Command||Executed DbCommand (15ms) [Parameters=[@__p_0='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='Text', CommandTimeout='30']
SELECT [s].[ID], [s].[IMGURL], [s].[JSONSTR], [s].[TITLE]
FROM [Sys_FormDesign_Template] AS [s]
ORDER BY [s].[ID] DESC
OFFSET @__p_0 ROWS FETCH NEXT @__p_1 ROWS ONLY|| || 00-59b7bb595a9fd73b2877e13257e9ffe3-c511c3ca3ba446c0-00 ||end
