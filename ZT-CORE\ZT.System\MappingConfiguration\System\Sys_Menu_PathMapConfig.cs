using ZT.Entity.MappingConfiguration;
using ZT.Entity.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class Sys_Menu_PathMapConfig : EntityMappingConfiguration<Sys_Menu_Path>
    {
    public override void Map(EntityTypeBuilder<Sys_Menu_Path>
        builderTable)
        {
        //b.Property(x => x.StorageName).HasMaxLength(45);
        }
        }
        }

