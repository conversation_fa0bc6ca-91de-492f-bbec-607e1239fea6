/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Repository提供数据库操作，如果要增加数据库操作请在当前目录下Partial文件夹Sys_columnAdjustmentRepository编写代码
 */
using ZT.System.IRepositories;
using ZT.Core.BaseProvider;
using ZT.Core.EFDbContext;
using ZT.Core.Extensions.AutofacManager;
using ZT.Entity.DomainModels;

namespace ZT.System.Repositories
{
    public partial class Sys_columnAdjustmentRepository : RepositoryBase<Sys_columnAdjustment> , ISys_columnAdjustmentRepository
    {
    public Sys_columnAdjustmentRepository(VOLContext dbContext)
    : base(dbContext)
    {

    }
    public static ISys_columnAdjustmentRepository Instance
    {
      get {  return AutofacContainerModule.GetService<ISys_columnAdjustmentRepository>(); } }
    }
}
