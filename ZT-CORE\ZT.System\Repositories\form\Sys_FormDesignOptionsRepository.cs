/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Repository提供数据库操作，如果要增加数据库操作请在当前目录下Partial文件夹Sys_FormDesignOptionsRepository编写代码
 */
using ZT.System.IRepositories;
using ZT.Core.BaseProvider;
using ZT.Core.EFDbContext;
using ZT.Core.Extensions.AutofacManager;
using ZT.System.DomainModels;

namespace ZT.System.Repositories
{
    public partial class Sys_FormDesignOptionsRepository : RepositoryBase<Sys_FormDesignOptions>
    , ISys_FormDesignOptionsRepository
    {
    public Sys_FormDesignOptionsRepository(VOLContext dbContext)
    : base(dbContext)
    {

    }
    public static ISys_FormDesignOptionsRepository Instance
    {
    get {  return AutofacContainerModule.GetService<ISys_FormDesignOptionsRepository>
        (); } }
        }
        }
