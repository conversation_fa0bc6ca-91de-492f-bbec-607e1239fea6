/*
*所有关于Sys_User_WorkstationCard类的业务代码接口应在此处编写
*/
using ZT.Core.BaseProvider;
using ZT.Entity.DomainModels;
using ZT.Core.Utilities;
using System.Linq.Expressions;
using ZT.AppManager.DomainModels.ApiEntity.Output;
using System.Collections.Generic;

namespace ZT.AppManager.IServices
{
    public partial interface ISys_User_WorkstationCardService
    {
        WebResponseContent GetCardList(int? uerid);
        WebResponseContent SaveCardList(List<ApiSys_User_WorkstationCardOutput> option, int? uerid);
        WebResponseContent DeleteCardList(int? uerid);
        WebResponseContent SearchCardList(int? uerid);
    }
}
