using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
//using {ParentTableNamespace}.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class PathMapConfig : EntityMappingConfiguration<Path>
    {
    public override void Map(EntityTypeBuilder<Path>
        builderTable)
        {
           //builderTable.HasOne<{ParentTableName}>(o => o.{TableTrueName}).WithOne().HasForeignKey<{ParentTableName}>(e => e.{Key});
           //builderTable.Navigation(o => o.{TableTrueName}).IsRequired();
        }
     }
}

