/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *Repository提供数据库操作，如果要增加数据库操作请在当前目录下Partial文件夹Sys_FormCollectionRepository编写代码
 */
using ZT.System.IRepositories;
using ZT.Core.BaseProvider;
using ZT.Core.EFDbContext;
using ZT.Core.Extensions.AutofacManager;
using ZT.System.DomainModels;

namespace ZT.System.Repositories
{
    public partial class Sys_FormCollectionRepository : RepositoryBase<Sys_FormCollection>
    , ISys_FormCollectionRepository
    {
    public Sys_FormCollectionRepository(VOLContext dbContext)
    : base(dbContext)
    {

    }
    public static ISys_FormCollectionRepository Instance
    {
    get {  return AutofacContainerModule.GetService<ISys_FormCollectionRepository>
        (); } }
        }
        }
