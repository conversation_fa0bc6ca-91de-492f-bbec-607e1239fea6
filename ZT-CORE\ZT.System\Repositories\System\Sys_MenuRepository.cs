﻿using ZT.System.IRepositories;
using ZT.Core.BaseProvider;
using ZT.Core.Extensions.AutofacManager;
using ZT.Core.EFDbContext;
using ZT.Entity.DomainModels;

namespace ZT.System.Repositories
{
    public partial class Sys_MenuRepository : RepositoryBase<Sys_Menu>, ISys_MenuRepository
    {
        public Sys_MenuRepository(VOLContext dbContext)
        : base(dbContext)
        {

        }
        public static ISys_MenuRepository Instance
        {
            get { return AutofacContainerModule.GetService<ISys_MenuRepository>(); }
        }
    }
}

