using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class Sys_FormDesign_TemplateMapConfig : EntityMappingConfiguration<Sys_FormDesign_Template>
    {
    public override void Map(EntityTypeBuilder<Sys_FormDesign_Template>
        builderTable)
        {
        //b.Property(x => x.StorageName).HasMaxLength(45);
        }
        }
        }

