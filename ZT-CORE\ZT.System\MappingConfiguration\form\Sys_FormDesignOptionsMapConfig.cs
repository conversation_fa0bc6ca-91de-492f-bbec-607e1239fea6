using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class Sys_FormDesignOptionsMapConfig : EntityMappingConfiguration<Sys_FormDesignOptions>
    {
    public override void Map(EntityTypeBuilder<Sys_FormDesignOptions>
        builderTable)
        {
        //b.Property(x => x.StorageName).HasMaxLength(45);
        }
        }
        }

