{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"ZT.Entity/2.1.0": {"dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "11.0.0", "Microsoft.EntityFrameworkCore": "7.0.20", "Microsoft.EntityFrameworkCore.Proxies": "7.0.20", "Newtonsoft.Json": "13.0.3", "Npgsql.EntityFrameworkCore.PostgreSQL": "3.1.0", "SqlSugarCore": "5.1.4.169", "WorkflowCore": "3.11.0"}, "runtime": {"ZT.Entity.dll": {}}}, "AutoMapper/11.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.0.0.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/11.0.0": {"dependencies": {"AutoMapper": "11.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.0.0.0"}}}, "Azure.Core/1.38.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.38.0.0", "fileVersion": "1.3800.24.12602"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "Castle.Core/5.1.0": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.0.0"}}}, "ConcurrentHashSet/1.1.0": {"runtime": {"lib/netstandard2.0/ConcurrentCollections.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/5.2.2": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "6.0.1", "System.Runtime.Caching": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.22.24240.6"}}, "resources": {"lib/net6.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net6.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net6.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "5.0.0.0", "fileVersion": "5.22.24240.6"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "5.0.0.0", "fileVersion": "5.22.24240.6"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.2.0.0"}}}, "Microsoft.Data.Sqlite/8.0.1": {"dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.1", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}}, "Microsoft.Data.Sqlite.Core/8.0.1": {"dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.123.58002"}}}, "Microsoft.EntityFrameworkCore/7.0.20": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.20", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.20", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "7.0.20.0", "fileVersion": "7.0.2024.26903"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.20": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "7.0.20.0", "fileVersion": "7.0.2024.26903"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.20": {}, "Microsoft.EntityFrameworkCore.Proxies/7.0.20": {"dependencies": {"Castle.Core": "5.1.0", "Microsoft.EntityFrameworkCore": "7.0.20"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Proxies.dll": {"assemblyVersion": "7.0.20.0", "fileVersion": "7.0.2024.26903"}}}, "Microsoft.EntityFrameworkCore.Relational/3.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.20"}, "runtime": {"lib/netstandard2.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "3.1.0.0", "fileVersion": "3.100.19.56505"}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.35.0", "System.Text.Encoding": "4.3.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Logging/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.IdentityModel.Tokens/6.35.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.35.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net6.0/MySqlConnector.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.2.5.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Npgsql/5.0.18": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net5.0/Npgsql.dll": {"assemblyVersion": "5.0.18.0", "fileVersion": "5.0.18.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/3.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.20", "Microsoft.EntityFrameworkCore.Abstractions": "7.0.20", "Microsoft.EntityFrameworkCore.Relational": "3.1.0", "Npgsql": "5.0.18"}, "runtime": {"lib/netstandard2.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "3.1.0.0", "fileVersion": "3.1.0.0"}}}, "OpenTelemetry.Api/1.1.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "6.0.1", "System.Reflection.Emit.Lightweight": "4.7.0"}, "runtime": {"lib/netstandard2.0/OpenTelemetry.Api.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.1.0.174"}}}, "Oracle.ManagedDataAccess.Core/3.21.100": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.1"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "3.1.21.1", "fileVersion": "3.1.21.1"}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "4.0.4.0", "fileVersion": "4.0.4.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.6", "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SQLitePCLRaw.core/2.1.6": {"dependencies": {"System.Memory": "4.5.4"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SqlSugarCore/5.1.4.169": {"dependencies": {"Microsoft.Data.SqlClient": "5.2.2", "Microsoft.Data.Sqlite": "8.0.1", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.3", "Npgsql": "5.0.18", "Oracle.ManagedDataAccess.Core": "3.21.100", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "8.6.0", "SqlSugarCore.Kdbndp": "9.3.6.801", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Text.RegularExpressions": "4.3.1"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "5.1.4.169", "fileVersion": "5.1.4.169"}}}, "SqlSugarCore.Dm/8.6.0": {"dependencies": {"System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/DM.DmProvider.dll": {"assemblyVersion": "8.3.1.27409", "fileVersion": "8.3.1.27409"}}}, "SqlSugarCore.Kdbndp/9.3.6.801": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "9.3.6.618", "fileVersion": "9.3.6.801"}}}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.24.5302"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1523.11507"}}}, "System.Diagnostics.EventLog/6.0.0": {}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.1423.7309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.1423.7309"}}}, "System.DirectoryServices.Protocols/6.0.1": {"runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.222.6406"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.35.0.0", "fileVersion": "6.35.0.41201"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.7.0": {}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.7.0": {}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Text.Encodings.Web/4.7.2": {}, "System.Text.Json/4.7.2": {}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "WorkflowCore/3.11.0": {"dependencies": {"ConcurrentHashSet": "1.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Newtonsoft.Json": "13.0.3", "OpenTelemetry.Api": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Linq.Queryable": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.TypeExtensions": "4.7.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0"}, "runtime": {"lib/netstandard2.0/WorkflowCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}}}, "libraries": {"ZT.Entity/2.1.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+596AnKykYCk9RxXCEF4GYuapSebQtFVvIA1oVG1rrRkCLAC7AkWehJ0brCfYUbdDW3v1H/p0W3hob7JoXGjMw==", "path": "automapper/11.0.0", "hashPath": "automapper.11.0.0.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0asw5WxdCFh2OTi9Gv+oKyH9SzxwYQSnO8TV5Dd0GggovILzJW4UimP26JAcxc3yB5NnC5urooZ1BBs8ElpiBw==", "path": "automapper.extensions.microsoft.dependencyinjection/11.0.0", "hashPath": "automapper.extensions.microsoft.dependencyinjection.11.0.0.nupkg.sha512"}, "Azure.Core/1.38.0": {"type": "package", "serviceable": true, "sha512": "sha512-IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "path": "azure.core/1.38.0", "hashPath": "azure.core.1.38.0.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "Castle.Core/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-31UJpTHOiWq95CDOHazE3Ub/hE/PydNWsJMwnEVTqFFP4WhAugwpaVGxzOxKgNeSUUeqS2W6lxV+q7u1pAOfXg==", "path": "castle.core/5.1.0", "hashPath": "castle.core.5.1.0.nupkg.sha512"}, "ConcurrentHashSet/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1SuuhZe0wUJbchXdjS23O+quojkw0IXqM9LtUp3RwgQreDI4cj1lfDbdHnD4fOYEnUrftzVIYoonvPlsXm30Bg==", "path": "concurrenthashset/1.1.0", "hashPath": "concurrenthashset.1.1.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mtoeRMh7F/OA536c/Cnh8L4H0uLSKB5kSmoi54oN7Fp0hNJDy22IqyMhaMH4PkDCqI7xL//Fvg9ldtuPHG0h5g==", "path": "microsoft.data.sqlclient/5.2.2", "hashPath": "microsoft.data.sqlclient.5.2.2.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w==", "path": "microsoft.data.sqlclient.sni.runtime/5.2.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.2.0.nupkg.sha512"}, "Microsoft.Data.Sqlite/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+7uDWNYZmLrVq9eABAKwy1phGbpoFVohKCUoh/nGg9WiBwi856EkAJYFiQhTJWoXxzpInkLFj/6KACoSB7ODYg==", "path": "microsoft.data.sqlite/8.0.1", "hashPath": "microsoft.data.sqlite.8.0.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s8C8xbwMb79EqzTaIhwiBrYtbv6ATnUW19pJed4fKVgN5K4VPQ7JUGqBLztknvD6EJIMKrfRnINGTjnZghrDGw==", "path": "microsoft.data.sqlite.core/8.0.1", "hashPath": "microsoft.data.sqlite.core.8.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-Wvmktob+dBPvMZdTj2yRrfDi3ERL/RRcE8EWjr3DuIjWn+6giqD81+oNM/09IthwZ3IsSMHgpiVVmTwAW92gEQ==", "path": "microsoft.entityframeworkcore/7.0.20", "hashPath": "microsoft.entityframeworkcore.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-ds6XFVrh34AWYAedlMo8tVkmxsxyXnwP0N6FTjlq4p1oR6TQktIabUDkSMKgStVYt4c4b4Rus3H/Oad9I4uSlQ==", "path": "microsoft.entityframeworkcore.abstractions/7.0.20", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-doouHLWFcYcd08QUh9+4aZu3sk4WySp/aSOHeIzNhMJ58ESQXUAGDvB1sjJ4HmE0tmCea51NGzgtHE5IBmFLGg==", "path": "microsoft.entityframeworkcore.analyzers/7.0.20", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Proxies/7.0.20": {"type": "package", "serviceable": true, "sha512": "sha512-b+PqL+F0MHRGxXi8hV7wbvXUFq7nsG5ZvVDiDmy8Ya7OyIYwfcQCQWxTcus1/bKib+aPZgxX3CGBRNoQqo3kIA==", "path": "microsoft.entityframeworkcore.proxies/7.0.20", "hashPath": "microsoft.entityframeworkcore.proxies.7.0.20.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-eeiqVqzXVuwQsUbqXMyXSEUn/EHB9zmDsr5f/+v6uEt0ir7pgItuIi3I7QV4xvM/s0KbFsqGUOrAFPeRHE3plg==", "path": "microsoft.entityframeworkcore.relational/3.1.0", "hashPath": "microsoft.entityframeworkcore.relational.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-65MrmXCziWaQFrI0UHkQbesrX5wTwf9XPjY5yFm/VkgJKFJ5gqvXRoXjIZcf2wLi5ZlwGz/oMYfyURVCWbM5iw==", "path": "microsoft.extensions.configuration.abstractions/2.2.0", "hashPath": "microsoft.extensions.configuration.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-xuR8E4Rd96M41CnUSCiOJ2DBh+z+zQSmyrYHdYhD6K4fXBcQGVnRCFQ0efROUYpP+p0zC1BLKr0JRpVuujTZSg==", "path": "microsoft.identitymodel.abstractions/6.35.0", "hashPath": "microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-9wxai3hKgZUb4/NjdRKfQd0QJvtXKDlvmGMYACbEC8DFaicMFCFhQFZq9ZET1kJLwZahf2lfY5Gtcpsx8zYzbg==", "path": "microsoft.identitymodel.jsonwebtokens/6.35.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-jePrSfGAmqT81JDCNSY+fxVWoGuJKt9e6eJ+vT7+quVS55nWl//jGjUQn4eFtVKt4rt5dXaleZdHRB9J9AJZ7Q==", "path": "microsoft.identitymodel.logging/6.35.0", "hashPath": "microsoft.identitymodel.logging.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "path": "microsoft.identitymodel.protocols/6.35.0", "hashPath": "microsoft.identitymodel.protocols.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "path": "microsoft.identitymodel.protocols.openidconnect/6.35.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-RN7lvp7s3Boucg1NaNAbqDbxtlLj5Qeb+4uSS1TeK5FSBVM40P4DKaTKChT43sHyKfh7V0zkrMph6DdHvyA4bg==", "path": "microsoft.identitymodel.tokens/6.35.0", "hashPath": "microsoft.identitymodel.tokens.6.35.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/5.0.18": {"type": "package", "serviceable": true, "sha512": "sha512-1u4iCPKL9wtPeSUzChIbgq/BlOhvf44o7xASacdpMY7z0PbqACKpNOF0fjEn9jDV/AJl/HtPY6zk5qasQ1URhw==", "path": "npgsql/5.0.18", "hashPath": "npgsql.5.0.18.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-RoT3YXmYpjGTPqYnENYAnKTgTe0/8IIDWpJ5JC+R/KgqS0/+XZDFMY0mV5yzQzjVt1rj/KChQ4ta7WLzqnI2/g==", "path": "npgsql.entityframeworkcore.postgresql/3.1.0", "hashPath": "npgsql.entityframeworkcore.postgresql.3.1.0.nupkg.sha512"}, "OpenTelemetry.Api/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-K4MkthPTNVAatWSldmRHq0oGB9Z9YZYNQg1xjualeG9yJTghUj18RjReNPabCmEqOpRmFI9mO6KS4L8QKFQKUg==", "path": "opentelemetry.api/1.1.0", "hashPath": "opentelemetry.api.1.1.0.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.100": {"type": "package", "serviceable": true, "sha512": "sha512-nsqyUE+v246WB0SOnR1u9lfZxYoNcdj1fRjTt7TOhCN0JurEc6+qu+mMe+dl1sySB2UpyWdfqHG1iSQJYaXEfA==", "path": "oracle.manageddataaccess.core/3.21.100", "hashPath": "oracle.manageddataaccess.core.3.21.100.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-BmAf6XWt4TqtowmiWe4/5rRot6GerAeklmOPfviOvwLoF5WwgxcJHAxZtySuyW9r9w+HLILnm8VfJFLCUJYW8A==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==", "path": "sqlitepclraw.core/2.1.6", "hashPath": "sqlitepclraw.core.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-2<PERSON>bJJLkIUIxRpOUlZNGuD4rICpBnrBR5anjyfUFQep4hMOIeqW+XGQYzrNmHSVz5xSWZ3klSbh7sFR6UyDj68Q==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-PQ2Oq3yepLY4P7ll145P3xtx2bX8xF4PzaKPRpw9jZlKvfe4LE/saAV82inND9usn1XRpmxXk7Lal3MTI+6CNg==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.6.nupkg.sha512"}, "SqlSugarCore/5.1.4.169": {"type": "package", "serviceable": true, "sha512": "sha512-xq9bDrHpaDVFvOQOf3K9RFRBwvORGmx/7pABd9SCU8lIaY9WdaHucT/pWa+OEH0ywgEO7z/MhufjguFohOENzw==", "path": "sqlsugarcore/5.1.4.169", "hashPath": "sqlsugarcore.5.1.4.169.nupkg.sha512"}, "SqlSugarCore.Dm/8.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-Q0NAjF9hvkxLbNedIrCqKd3uru0enzZ49GaQtenvsLDQ29aHwlSqg1mRkVYxZ/85UYJFgXh+XHqABSrMgun4aw==", "path": "sqlsugarcore.dm/8.6.0", "hashPath": "sqlsugarcore.dm.8.6.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/9.3.6.801": {"type": "package", "serviceable": true, "sha512": "sha512-FM8e18+lVENoG1qK1sK0TuIuCL1ZT/4dWF0wVvh3q03WU5Eu8l+7YP/qJvg9nRP034U1shgJAETpoHq2uXAHmQ==", "path": "sqlsugarcore.kdbndp/9.3.6.801", "hashPath": "sqlsugarcore.kdbndp.9.3.6.801.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ndUZlEkAMc1XzM0xGN++SsJrNhRkIHaKI8+te325vrUgoLT1ufWNI6KB8FFrL7NpRMHPrdxP99aF3fHbAPxW0A==", "path": "system.directoryservices.protocols/6.0.1", "hashPath": "system.directoryservices.protocols.6.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-yxGIQd3BFK7F6S62/7RdZk3C/mfwyVxvh6ngd1VYMBmbJ1YZZA9+Ku6suylVtso0FjI0wbElpJ0d27CdsyLpBQ==", "path": "system.identitymodel.tokens.jwt/6.35.0", "hashPath": "system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "path": "system.reflection.emit.lightweight/4.7.0", "hashPath": "system.reflection.emit.lightweight.4.7.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VybpaOQQhqE6siHppMktjfGBw1GCwvCqiufqmP8F1nj7fTUNtW35LOEt3UZTEsECfo+ELAl/9o9nJx3U91i7vA==", "path": "system.reflection.typeextensions/4.7.0", "hashPath": "system.reflection.typeextensions.4.7.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "WorkflowCore/3.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-ebWb6/neCM6cYFqy8+OkfYEnYPN62Dc2MyOukXM4JTUbwhuYcFalbO+YPjUnQyyA3BkAKYqsEZ1PzEOFNC3sMA==", "path": "workflowcore/3.11.0", "hashPath": "workflowcore.3.11.0.nupkg.sha512"}}}