using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
//using {ParentTableNamespace}.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class Sys_SettingMapConfig : EntityMappingConfiguration<Sys_Setting>
    {
    public override void Map(EntityTypeBuilder<Sys_Setting>
        builderTable)
        {
           //builderTable.HasOne<{ParentTableName}>(o => o.{TableTrueName}).WithOne().HasForeignKey<{ParentTableName}>(e => e.{Key});
           //builderTable.Navigation(o => o.{TableTrueName}).IsRequired();
        }
     }
}

