using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ZT.Entity.DomainModels;

namespace ZT.System.MappingConfiguration
{
    public class Sys_LangMapConfig : EntityMappingConfiguration<Sys_Lang>
    {
    public override void Map(EntityTypeBuilder<Sys_Lang>
        builderTable)
        {
        //b.Property(x => x.StorageName).HasMaxLength(45);
        }
        }
        }

