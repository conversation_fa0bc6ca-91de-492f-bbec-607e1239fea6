/*
*所有关于Sys_FormDesignOptions类的业务代码接口应在此处编写
*/
using ZT.Core.BaseProvider;
using ZT.Entity.DomainModels;
using ZT.Core.Utilities;
using System.Linq.Expressions;
using ZT.System.DomainModels;

namespace ZT.System.IServices
{
    public partial interface ISys_FormDesignOptionsService
    {
        public WebResponseContent FormPublish(SaveModel saveModel);
        WebResponseContent GetDataTableDataSource();
        WebResponseContent GetDataBaseEntities();
        WebResponseContent CheckExist(Sys_FormDesignOptions saveModel);
    }
 }
