using ZT.Entity.MappingConfiguration;
using ZT.AppManager.DomainModels;
//using {ParentTableNamespace}.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.AppManager.MappingConfiguration
{
    public class Sys_WorkstationCardMapConfig : EntityMappingConfiguration<Sys_WorkstationCard>
    {
    public override void Map(EntityTypeBuilder<Sys_WorkstationCard>
        builderTable)
        {
           //builderTable.HasOne<{ParentTableName}>(o => o.{TableTrueName}).WithOne().HasForeignKey<{ParentTableName}>(e => e.{Key});
           //builderTable.Navigation(o => o.{TableTrueName}).IsRequired();
        }
     }
}

