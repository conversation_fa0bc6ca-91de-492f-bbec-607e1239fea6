﻿/*
 *Author：jxx
 *Contact：<EMAIL>
 *Date：2018-07-01
 * 此代码由框架生成，请勿随意更改
 */
using ZT.System.IRepositories;
using ZT.Core.BaseProvider;
using ZT.Core.EFDbContext;
using ZT.Core.Extensions.AutofacManager;
using ZT.Entity.DomainModels;

namespace ZT.System.Repositories
{
    public partial class Sys_UserRepository : RepositoryBase<Sys_User>, ISys_UserRepository
    {
        public Sys_UserRepository(VOLContext dbContext)
        : base(dbContext)
        {

        }
        public static ISys_UserRepository Instance
        {
            get { return AutofacContainerModule.GetService<ISys_UserRepository>(); }
        }
    }
}

