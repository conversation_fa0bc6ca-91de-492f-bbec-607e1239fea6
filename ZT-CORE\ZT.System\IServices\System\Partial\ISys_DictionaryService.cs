﻿using System.Collections.Generic;
using System.Threading.Tasks;
using ZT.Core.BaseProvider;
using ZT.Core.Utilities;
using ZT.Entity.DomainModels;
using ZT.Entity.DomainModels.ApiEntity.Input;

namespace ZT.System.IServices
{
    public partial interface ISys_DictionaryService
    {
        /// <summary>
        /// 代码生成器获取所有字典项(超级管理权限)
        /// </summary>
        /// <returns></returns>
        Task<WebResponseContent> GetBuilderDictionary();
        object GetVueDictionary(ApiSys_DictionaryInput[] dicInputs);
        object GetTableDictionary(Dictionary<string, object[]> keyData);
        object GetSearchDictionary(ApiSys_DictionaryInput dicInput);

        /// <summary>
        /// 表单设置为远程查询，重置或第一次添加表单时，获取字典的key、value
        /// </summary>
        /// <param name="dicNo"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        Task<object> GetRemoteDefaultKeyValue(string dicNo, string key);
    }
}

