/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果要增加方法请在当前目录下Partial文件夹SYS_WORKFLOWDESIGNOPTIONSController编写
 */
using Microsoft.AspNetCore.Mvc;
using ZT.Core.Controllers.Basic;
using ZT.Entity.AttributeManager;
using ZT.System.IServices;

namespace ZT.System.Controllers
{
    [Route("api/SYS_WORKFLOWDESIGNOPTIONS")]
    [PermissionTable(Name = "SYS_WORKFLOWDESIGNOPTIONS")]
    public partial class SYS_WORKFLOWDESIGNOPTIONSController : ApiBaseController<ISYS_WORKFLOWDESIGNOPTIONSService>
    {
        public SYS_WORKFLOWDESIGNOPTIONSController(ISYS_WORKFLOWDESIGNOPTIONSService service)
    : base(service)
    {
    }
    }
    }

