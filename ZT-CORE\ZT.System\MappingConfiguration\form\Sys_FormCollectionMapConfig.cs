using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class Sys_FormCollectionMapConfig : EntityMappingConfiguration<Sys_FormCollection>
    {
    public override void Map(EntityTypeBuilder<Sys_FormCollection>
        builderTable)
        {
        //b.Property(x => x.StorageName).HasMaxLength(45);
        }
        }
        }

