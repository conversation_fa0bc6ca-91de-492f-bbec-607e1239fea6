﻿using System;
using System.Collections.Generic;
using System.Text;
using ZT.Entity.SystemModels;

namespace ZT.AppManager.DomainModels.ApiEntity.Output
{
    public class ApiSys_User_WorkstationCardOutput:IApiBaseEntity
    {
        public int id { get; set; }
        public string title { get; set; }
        public int? x { get; set; }
        public int? y { get; set; }
        public int? h { get; set; }
        public int? w { get; set; }
        public string color { get; set; }
        public string path { get; set; }
        public string description { get; set; }
        public int? pageSize { get; set; }
        public string defaultTab { get; set; }
        public string icon { get; set; }
        public string defaultTab_dicNo { get; set; }
    }
}
