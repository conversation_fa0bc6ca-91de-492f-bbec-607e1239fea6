/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using ZT.Entity;
using ZT.Entity.SystemModels;
using ZT.Entity.DomainModels;
using ZT.Entity.AttributeManager;
using ZT.System.DomainModels;


namespace ZT.System.DomainModels
{
    [Entity(TableCnName = "工作流",TableName = "SYS_WORKFLOWDESIGNOPTIONS")]
    [Table("SYS_WORKFLOWDESIGNOPTIONS")]
    [SugarTable("SYS_WORKFLOWDESIGNOPTIONS")]
    public class SYS_WORKFLOWDESIGNOPTIONS : BaseEntity
    {
        /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.WORKFLOWID
       /// </summary>
       [Key]
       [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.WORKFLOWID")]
       [Column(TypeName="int")]
       [Required(AllowEmptyStrings=false)]
       public int WORKFLOWID { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.NAME
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.NAME")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       [Editable(true)]
       public string? NAME { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.ICON
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.ICON")]
       [MaxLength(100)]
       [Column(TypeName="nvarchar(100)")]
       [Editable(true)]
       public string? ICON { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.GROUPID
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.GROUPID")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? GROUPID { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.FORMCONF
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.FORMCONF")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string? FORMCONF { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.PROCESS
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.PROCESS")]
       [Column(TypeName="nvarchar(max)")]
       [Editable(true)]
       public string? PROCESS { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.REMARK
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.REMARK")]
       [MaxLength(2000)]
       [Column(TypeName="nvarchar(2000)")]
       [Editable(true)]
       public string? REMARK { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.CREATEDATE
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.CREATEDATE")]
       [Column(TypeName="datetime")]
       public DateTime? CREATEDATE { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.CREATEID
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.CREATEID")]
       [Column(TypeName="int")]
       public int? CREATEID { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.CREATOR
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.CREATOR")]
       [MaxLength(60)]
       [Column(TypeName="varchar(60)")]
       public string? CREATOR { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.MODIFIER
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.MODIFIER")]
       [MaxLength(60)]
       [Column(TypeName="varchar(60)")]
       public string? MODIFIER { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.MODIFYDATE
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.MODIFYDATE")]
       [Column(TypeName="datetime")]
       public DateTime? MODIFYDATE { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.MODIFYID
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.MODIFYID")]
       [Column(TypeName="int")]
       public int? MODIFYID { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.WORKFLOWSTATUS
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.WORKFLOWSTATUS")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? WORKFLOWSTATUS { get; set; }

       /// <summary>
       ///SYSWORKFLOWDESIGNOPTIONS.WORKFLOWREVISION
       /// </summary>
       [Display(Name ="SYSWORKFLOWDESIGNOPTIONS.WORKFLOWREVISION")]
       [MaxLength(60)]
       [Column(TypeName="varchar(60)")]
       [Editable(true)]
       public string? WORKFLOWREVISION { get; set; }

       
    }
}