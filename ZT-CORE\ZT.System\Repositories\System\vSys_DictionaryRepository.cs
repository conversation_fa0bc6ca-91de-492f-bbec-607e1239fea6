﻿/*
 *Author：jxx
 *Contact：<EMAIL>
 *Date：2018-07-01
 * 此代码由框架生成，请勿随意更改
 */
using ZT.System.IRepositories;
using ZT.Core.BaseProvider;
using ZT.Core.EFDbContext;
using ZT.Core.Extensions.AutofacManager;
using ZT.Entity.DomainModels;

namespace ZT.System.Repositories
{
    public partial class vSys_DictionaryRepository : RepositoryBase<vSys_Dictionary>, IvSys_DictionaryRepository
    {
        public vSys_DictionaryRepository(VOLContext dbContext)
        : base(dbContext)
        {

        }
        public static IvSys_DictionaryRepository Instance
        {
            get { return AutofacContainerModule.GetService<IvSys_DictionaryRepository>(); }
        }
    }
}

