/*
 *代码由框架生成,此处任何更改都可能导致被代码生成器覆盖
 *所有业务编写全部应在Partial文件夹下SYS_WORKFLOWDESIGNOPTIONSService与ISYS_WORKFLOWDESIGNOPTIONSService中编写
 */
using ZT.System.IRepositories;
using ZT.System.IServices;
using ZT.Core.BaseProvider;
using ZT.Core.Extensions.AutofacManager;
using ZT.System.DomainModels;

namespace ZT.System.Services
{
    public partial class SYS_WORKFLOWDESIGNOPTIONSService : ServiceBase<SYS_WORKFLOWDESIGNOPTIONS, ISYS_WORKFLOWDESIGNOPTIONSRepository>
    , ISYS_WORKFLOWDESIGNOPTIONSService, IDependency
    {
    public SYS_WORKFLOWDESIGNOPTIONSService(ISYS_WORKFLOWDESIGNOPTIONSRepository repository)
    : base(repository)
    {
    Init(repository);
    }
    public static ISYS_WORKFLOWDESIGNOPTIONSService Instance
    {
    get { return AutofacContainerModule.GetService<ISYS_WORKFLOWDESIGNOPTIONSService>
        (); } }
        }
        }
