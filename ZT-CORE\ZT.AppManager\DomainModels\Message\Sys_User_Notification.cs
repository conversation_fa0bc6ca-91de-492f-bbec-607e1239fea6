/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using ZT.Entity;
using ZT.Entity.SystemModels;
using ZT.Entity.DomainModels;
using ZT.Entity.AttributeManager;
using ZT.AppManager.DomainModels;


namespace ZT.AppManager.DomainModels
{
    [Entity(TableCnName = "Notification.UserNotification",TableName = "SYS_USER_NOTIFICATION")]
    [Table("SYS_USER_NOTIFICATION")]
    [SugarTable("SYS_USER_NOTIFICATION")]
    public class Sys_User_Notification : BaseEntity
    {
        /// <summary>
       ///Notification.Content
       /// </summary>
       [Display(Name ="Notification.Content")]
       [MaxLength(500)]
       [NotMapped]
        [SugarColumn(IsIgnore =true)]
       public string? CONTENT { get; set; }

       /// <summary>
       ///Role.CreateDate
       /// </summary>
       [Display(Name ="Role.CreateDate")]
       [NotMapped]
        [SugarColumn(IsIgnore =true)]
       public DateTime? CREATEDATE { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Key]
       [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
       [Display(Name ="USER_NOTIFICATION_ID")]
       [Column(TypeName="int")]
       [Required(AllowEmptyStrings=false)]
       public int USER_NOTIFICATION_ID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="USER_ID")]
       [Column(TypeName="int")]
       [Required(AllowEmptyStrings=false)]
       public int USER_ID { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="NOTIFICATION_ID")]
       [Column(TypeName="int")]
       [Required(AllowEmptyStrings=false)]
       public int NOTIFICATION_ID { get; set; }

       [Display(Name ="NOTIFICATION")]
       [ForeignKey("NOTIFICATION_ID")]
       [Navigate(NavigateType.OneToOne, nameof(Sys_User_Notification.NOTIFICATION_ID))]
       public Sys_Notification? NOTIFICATION { get; set; }
/// <summary>
       ///
       /// </summary>
       [Display(Name ="STATUS")]
       [Column(TypeName="int")]
       [Required(AllowEmptyStrings=false)]
       public int STATUS { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="MODIFYDATE")]
       [Column(TypeName="datetime")]
       public DateTime? MODIFYDATE { get; set; }

       /// <summary>
       ///
       /// </summary>
       [Display(Name ="MODIFIER")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       public string? MODIFIER { get; set; }

       
    }
}