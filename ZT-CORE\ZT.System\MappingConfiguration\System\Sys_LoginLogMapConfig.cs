using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class Sys_LoginLogMapConfig : EntityMappingConfiguration<Sys_LoginLog>
    {
    public override void Map(EntityTypeBuilder<Sys_LoginLog>
        builderTable)
        {
        //b.Property(x => x.StorageName).HasMaxLength(45);
        }
        }
        }

