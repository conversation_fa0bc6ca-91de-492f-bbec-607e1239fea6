/*
*所有关于Sys_WorkstationCard类的业务代码接口应在此处编写
*/
using ZT.Core.BaseProvider;
using ZT.Entity.DomainModels;
using ZT.Core.Utilities;
using System.Linq.Expressions;
using ZT.AppManager.DomainModels;
using System.Collections.Generic;

namespace ZT.AppManager.IServices
{
    public partial interface ISys_WorkstationCardService
    {
        Sys_WorkstationCard GetOneById(int sYS_WORKSTATIONCARD_ID);
        List<Sys_WorkstationCard> GetListById(int[] sYS_WORKSTATIONCARD_IDs);
    }
}
