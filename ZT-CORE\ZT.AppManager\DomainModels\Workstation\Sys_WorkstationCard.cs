/*
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *如果数据库字段发生变化，请在代码生器重新生成此Model
 */
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using ZT.Entity;
using ZT.Entity.SystemModels;
using ZT.Entity.DomainModels;
using ZT.Entity.AttributeManager;
using ZT.AppManager.DomainModels;


namespace ZT.AppManager.DomainModels
{
    [Entity(TableCnName = "router.workstation",TableName = "SYS_WORKSTATIONCARD")]
    [Table("SYS_WORKSTATIONCARD")]
    [SugarTable("SYS_WORKSTATIONCARD")]
    public class Sys_WorkstationCard : BaseEntity
    {
        /// <summary>
       ///SysWorkstationCard.SYS_WORKSTATIONCARDNAME
       /// </summary>
       [Display(Name ="SysWorkstationCard.SYS_WORKSTATIONCARDNAME")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public string SYS_WORKSTATIONCARDNAME { get; set; }

       /// <summary>
       ///Menu.MenuPath
       /// </summary>
       [Display(Name ="Menu.MenuPath")]
       [Column(TypeName="int")]
       [Editable(true)]
       [Required(AllowEmptyStrings=false)]
       public int PATHID { get; set; }

       [Display(Name ="PATH")]
       [ForeignKey("PATHID")]
       [Navigate(NavigateType.OneToOne, nameof(Sys_WorkstationCard.PATHID))]
       public Sys_Menu_Path? PATH { get; set; }
/// <summary>
       ///Menu.Icon
       /// </summary>
       [Display(Name ="Menu.Icon")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       [Editable(true)]
       public string? ICON { get; set; }

       /// <summary>
       ///SysWorkstationCard.DESCRIPTION
       /// </summary>
       [Display(Name ="SysWorkstationCard.DESCRIPTION")]
       [MaxLength(200)]
       [Column(TypeName="varchar(200)")]
       [Editable(true)]
       public string? DESCRIPTION { get; set; }

       /// <summary>
       ///SysWorkstationCard.PAGESIZE
       /// </summary>
       [Display(Name ="SysWorkstationCard.PAGESIZE")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? PAGESIZE { get; set; }

       /// <summary>
       ///SysWorkstationCard.DEFAULTTAB_DICNO
       /// </summary>
       [Display(Name ="SysWorkstationCard.DEFAULTTAB_DICNO")]
       [MaxLength(100)]
       [Column(TypeName="varchar(100)")]
       [Editable(true)]
       public string? DEFAULTTAB_DICNO { get; set; }

       /// <summary>
       ///SysWorkstationCard.ENABLE
       /// </summary>
       [Display(Name ="SysWorkstationCard.ENABLE")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ENABLE { get; set; }

       /// <summary>
       ///SysWorkstationCard.ORDERNO
       /// </summary>
       [Display(Name ="SysWorkstationCard.ORDERNO")]
       [Column(TypeName="int")]
       [Editable(true)]
       public int? ORDERNO { get; set; }

       /// <summary>
       ///common.creator
       /// </summary>
       [Display(Name ="common.creator")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       public string? CREATOR { get; set; }

       /// <summary>
       ///common.createDate
       /// </summary>
       [Display(Name ="common.createDate")]
       [Column(TypeName="datetime")]
       public DateTime? CREATEDATE { get; set; }

       /// <summary>
       ///common.modifier
       /// </summary>
       [Display(Name ="common.modifier")]
       [MaxLength(50)]
       [Column(TypeName="varchar(50)")]
       public string? MODIFIER { get; set; }

       /// <summary>
       ///common.modifyDate
       /// </summary>
       [Display(Name ="common.modifyDate")]
       [Column(TypeName="datetime")]
       public DateTime? MODIFYDATE { get; set; }

       /// <summary>
       ///SysWorkstationCard.SYS_WORKSTATIONCARD_ID
       /// </summary>
       [Key]
       [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
       [Display(Name ="SysWorkstationCard.SYS_WORKSTATIONCARD_ID")]
       [Column(TypeName="int")]
       [Required(AllowEmptyStrings=false)]
       public int SYS_WORKSTATIONCARD_ID { get; set; }

       
    }
}