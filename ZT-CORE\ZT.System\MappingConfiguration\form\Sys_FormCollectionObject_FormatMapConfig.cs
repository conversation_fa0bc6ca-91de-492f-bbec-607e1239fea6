using ZT.Entity.MappingConfiguration;
using ZT.System.DomainModels;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace ZT.System.MappingConfiguration
{
    public class Sys_FormCollection_FormatMapConfig : EntityMappingConfiguration<Sys_FormCollection_Format>
    {
    public override void Map(EntityTypeBuilder<Sys_FormCollection_Format>
        builderTable)
        {
        //b.Property(x => x.StorageName).HasMaxLength(45);
        }
        }
        }

